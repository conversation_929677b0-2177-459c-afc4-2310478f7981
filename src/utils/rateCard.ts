import mongoose from 'mongoose';
import RateCardSchema, { IRateCard } from '../models/rate-card.model';
import logger from './logger';

export interface RateCardQuery {
  packageId: mongoose.Types.ObjectId;
  startDate?: string;
  endDate?: string;
  startDateTime?: string;
  endDateTime?: string;
}

export interface RateCardResult {
  packageId: string;
  price: number;
}

/**
 * Fetches rate cards for given package queries and returns a map of packageId to price
 * Supports both date-based and dateTime-based queries
 * 
 * @param queries - Array of rate card queries containing packageId and date/dateTime ranges
 * @param priceStrategy - 'highest' to get the highest price, 'first' to get the first matching price
 * @returns Record mapping packageId to price
 */
export async function getRateCardPricing(
  queries: RateCardQuery[],
  priceStrategy: 'highest' | 'first' = 'first'
): Promise<Record<string, number>> {
  try {
    if (!queries || queries.length === 0) {
      return {};
    }

    // Separate queries based on whether they use date or dateTime
    const dateQueries = queries.filter(q => q.startDate && q.endDate);
    const dateTimeQueries = queries.filter(q => q.startDateTime && q.endDateTime);

    let rateCards: IRateCard[] = [];

    // Handle date-based queries (for block booking)
    if (dateQueries.length > 0) {
      const dateRateCards = await RateCardSchema.find({
        $or: dateQueries.map((query) => ({
          packageId: query.packageId,
          date: { $gte: query.startDate, $lte: query.endDate },
        })),
      }).lean();
      rateCards = rateCards.concat(dateRateCards);
    }

    // Handle dateTime-based queries (for availability check)
    if (dateTimeQueries.length > 0) {
      const dateTimeRateCards = await RateCardSchema.find({
        $or: dateTimeQueries.map((query) => ({
          packageId: query.packageId,
          dateTime: {
            $gte: new Date(query.startDateTime!).toISOString(),
            $lte: new Date(query.endDateTime!).toISOString(),
          },
        })),
      }).lean();
      rateCards = rateCards.concat(dateTimeRateCards);
    }

    // Build rate card map based on strategy
    const rateCardMap = rateCards.reduce(
      (acc, rateCard) => {
        const packageId = rateCard.packageId.toString();
        
        if (priceStrategy === 'highest') {
          // For availability check - get the highest price
          if (!acc[packageId] || rateCard.price > acc[packageId]) {
            acc[packageId] = rateCard.price;
          }
        } else {
          // For block booking - get the first matching price
          if (!acc[packageId]) {
            acc[packageId] = rateCard.price;
          }
        }
        
        return acc;
      },
      {} as Record<string, number>
    );

    logger.info(`Rate card pricing fetched for ${Object.keys(rateCardMap).length} packages`);
    return rateCardMap;

  } catch (error) {
    logger.error(`Failed to fetch rate card pricing: ${(error as Error).message}`);
    throw error;
  }
}

/**
 * Builds rate card queries for availability check (uses dateTime)
 * 
 * @param packageIds - Array of package IDs
 * @param startDateTime - Start date time string
 * @param endDateTime - End date time string
 * @returns Array of rate card queries
 */
export function buildAvailabilityRateCardQueries(
  packageIds: mongoose.Types.ObjectId[],
  startDateTime: string,
  endDateTime: string
): RateCardQuery[] {
  return packageIds.map(packageId => ({
    packageId,
    startDateTime,
    endDateTime,
  }));
}

/**
 * Builds rate card queries for block booking (uses date)
 * 
 * @param reservations - Array of reservation details with packageId, startDateTime, endDateTime
 * @returns Array of rate card queries (filters out invalid dates)
 */
export function buildBlockBookingRateCardQueries(
  reservations: Array<{
    packageId: string | mongoose.Types.ObjectId;
    startDateTime: Date | string;
    endDateTime: Date | string;
  }>
): RateCardQuery[] {
  return reservations
    .map((reservation) => {
      const startDate = new Date(reservation.startDateTime);
      const endDate = new Date(reservation.endDateTime);
      
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        logger.warn(
          `Invalid dates for reservation: packageId=${reservation.packageId}`
        );
        return null;
      }
      
      return {
        packageId: new mongoose.Types.ObjectId(reservation.packageId.toString()),
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
      };
    })
    .filter((query): query is RateCardQuery => query !== null);
}

/**
 * Gets rate card price for a specific package, falls back to package price if no rate card found
 * 
 * @param packageId - Package ID to get price for
 * @param rateCardMap - Map of packageId to rate card price
 * @param fallbackPrice - Fallback price if no rate card found
 * @returns Final price to use
 */
export function getRateCardPrice(
  packageId: string,
  rateCardMap: Record<string, number>,
  fallbackPrice: number = 0
): number {
  return rateCardMap[packageId] !== undefined 
    ? rateCardMap[packageId] 
    : fallbackPrice;
}
