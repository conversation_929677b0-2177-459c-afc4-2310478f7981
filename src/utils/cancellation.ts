import { Response } from 'express';
import ReservationModel, { IReservation } from '../models/reservation.model';
import propertyModel, { IProperty } from '../models/property.model';
import PaymentModel, { IPayment } from '../models/payment.model';
import logger from './logger';

interface RefundValidationResult {
  reservation: IReservation;
  property: IProperty;
  cancellationPolicies: RefundPolicy[];
  hoursDifference: number;
  refundPolicy: RefundPolicy;
  refundableAmount: number;
}

type RefundPolicy = {
  hours: number;
  refund_percent: number;
  description: string;
};

export function findRefundPolicy(
  remaining_hours: number,
  policies: RefundPolicy[],
): {
  hours: number;
  refund_percent: number;
  description: string;
} {
  let eligible_policy = null;
  let max_refund = -1;

  for (const policy of policies) {
    if (remaining_hours >= policy.hours && policy.refund_percent > max_refund) {
      eligible_policy = policy;
      max_refund = policy.refund_percent;
    }
  }

  if (eligible_policy) {
    return {
      hours: eligible_policy.hours,
      refund_percent: eligible_policy.refund_percent,
      description: eligible_policy.description,
    };
  } else {
    return {
      hours: 0,
      refund_percent: 0,
      description: 'No refund',
    };
  }
}

export async function validateRefundInputs(
  reservationId: string,
  cancellationTime: string | undefined,
  res: Response,
  useGroupReservationId: boolean = false,
): Promise<RefundValidationResult | null> {
  if (!cancellationTime) {
    logger.warn(`Cancellation time not provided`);
    res.status(400).json({ error: 'Cancellation time not provided' });
    return null;
  }

  const reservation = useGroupReservationId
    ? await ReservationModel.findOne({ groupReservationId: reservationId })
    : await ReservationModel.findById(reservationId);

  if (!reservation) {
    logger.warn(`Reservation with id ${reservationId} not found`);
    res.status(404).json({ error: 'Reservation not found' });
    return null;
  }

  if (
    reservation.reservations.some(
      (reservationData) =>
        new Date(reservationData.startDateTime).getTime() < Date.now(),
    )
  ) {
    res.status(400).json({ error: 'Cannot update past reservation' });
    return null;
  }

  const cancellationDate = new Date(cancellationTime);
  const checkInDate = reservation.reservations[0].startDateTime;
  const timeDifference = checkInDate.getTime() - cancellationDate.getTime();
  const hoursDifference = timeDifference / (1000 * 60 * 60);

  const property = await propertyModel.findById(reservation.propertyId);
  if (!property) {
    logger.warn(`Property with id ${reservation.propertyId} not found`);
    res.status(404).json({ error: 'Property not found' });
    return null;
  }

  const customFields = property.customFields as unknown as Map<
    string,
    RefundPolicy[]
  >;
  const cancellationPolicies = customFields.get('cancellationPolicy') ?? [];

  if (
    !cancellationPolicies ||
    !Array.isArray(cancellationPolicies) ||
    cancellationPolicies.length === 0
  ) {
    logger.warn(
      `Cancellation policies not found for property ${property.name}`,
    );
    res.status(404).json({ error: 'Cancellation policies not found' });
    return null;
  }

  const refundPolicy = findRefundPolicy(hoursDifference, cancellationPolicies);
  const refundableAmount = refundPolicy.refund_percent
    ? (refundPolicy.refund_percent / 100) *
      reservation.reservations.reduce(
        (acc: number, reservationData) => acc + reservationData.totalAmount,
        0,
      )
    : 0;

  return {
    reservation,
    property,
    cancellationPolicies,
    hoursDifference,
    refundPolicy,
    refundableAmount,
  };
}

export async function validatePayment(
  groupReservationId: string,
  res: Response,
): Promise<IPayment | null> {
  const payment = await PaymentModel.findOne({
    reservationId: groupReservationId,
    status: 'paid',
  });

  if (!payment) {
    logger.warn(
      `Payment with Group Reservation id ${groupReservationId} not found`,
    );
    res.status(404).json({ error: 'Payment not found' });
    return null;
  }

  if (payment.status === 'refunded') {
    logger.warn(
      `Payment with Group Reservation id ${groupReservationId} already refunded`,
    );
    res.status(400).json({ error: 'Payment already refunded' });
    return null;
  }

  return payment;
}
