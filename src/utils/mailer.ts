import nodemailer from 'nodemailer';
import logger from './logger';
import {
  ERROR_EMAIL_RECIPIENT,
  MAIL_FROM,
  MAIL_IS_SECURE,
  MAIL_PASSWORD,
  MAIL_SMTP_HOST,
  <PERSON><PERSON>_SMTP_PORT,
  MAIL_USERNAME,
} from '../constants';

export const sendErrorEmail = async (
  subject: string,
  errorMessage: string,
): Promise<void> => {
  try {
    const transporter = nodemailer.createTransport({
      host: MAIL_SMTP_HOST,
      port: MAIL_SMTP_PORT,
      requireTLS: true,
      secure: MAIL_IS_SECURE,
      auth: {
        user: MAIL_USERNAME,
        pass: MAIL_PASSWORD,
      },
    });
    const nodeMailerTransport = {
      from: MAIL_FROM,
      to: ERROR_EMAIL_RECIPIENT,
      subject: subject,
      html: errorMessage,
    };
    await transporter.sendMail(nodeMailerTransport);
    logger.info(`Error email sent: ${subject}`);
  } catch (error) {
    logger.error(`Failed to send error email: `, error);
  }
};
