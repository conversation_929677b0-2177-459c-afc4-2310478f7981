import mongoose, { Schema, Document } from 'mongoose';
// import { ammenitySchema, IAmmenity } from './room-type.model';
import Sequence from './sequence.model';

export interface IPackage extends Document {
  active: boolean;
  _id: string;
  code: string;
  name: string;
  description: string;
  duration: number;
  noOfAdults: number;
  noOfChildren: number;
  propertyId: mongoose.Types.ObjectId;
  roomTypeId: mongoose.Types.ObjectId;
  price: number;
  taxes: mongoose.Types.ObjectId[];
  amenities: mongoose.Types.ObjectId[];
  customFields: Map<string, unknown>;
}

// export interface ITax extends Document {
//   name: string;
//   percentage: number;
// }

// export const taxSchema = new Schema<ITax>({
//   name: { type: String, required: true },
//   percentage: { type: Number, required: true },
// }, { _id: false });

const packageSchema: Schema = new Schema<IPackage>(
  {
    active: { type: Boolean, required: true, default: true },
    code: { type: String, required: false },
    name: { type: String, required: true },
    description: { type: String, required: false, default: '' },
    duration: { type: Number, required: true },
    propertyId: {
      type: Schema.Types.ObjectId,
      ref: 'Property',
      required: true,
    },
    roomTypeId: {
      type: Schema.Types.ObjectId,
      ref: 'RoomType',
      required: true,
    },
    price: { type: Number, required: true },
    taxes: {
      type: [Schema.Types.ObjectId],
      ref: 'DomainValue',
      required: false,
    },
    amenities: {
      type: [Schema.Types.ObjectId],
      ref: 'DomainValue',
      required: false,
    },
    noOfAdults: { type: Number, required: true, default: 1 },
    noOfChildren: { type: Number, required: true, default: 0 },
    customFields: {
      type: Map,
      of: Object,
    },
  },
  {
    timestamps: true,
  },
);

packageSchema.index({ code: 1, propertyId: 1 }, { unique: true });
packageSchema.index({ name: 1, propertyId: 1 }, { unique: true });

packageSchema.pre<IPackage>('save', async function (next) {
  try {
    if (this.isNew) {
      const sequence = await Sequence.findOneAndUpdate(
        { name: 'packageCode', propertyId: this.propertyId },
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true, setDefaultsOnInsert: true },
      );

      if (!sequence) throw new Error('Failed to generate sequence number');

      this.code = sequence.sequence_value.toString().padStart(4, '0');
    }
    next();
  } catch (err) {
    next(err instanceof Error ? err : new Error(String(err)));
  }
});

export default mongoose.model<IPackage>('Package', packageSchema, 'Package');
