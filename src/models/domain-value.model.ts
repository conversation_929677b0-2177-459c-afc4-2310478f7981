import mongoose, { Schema, Document } from 'mongoose';

export interface IDomainValue extends Document {
  propertyId: string;
  category: string;
  level: string;
  code: string;
  name: string;
  description?: string;
  value: string;
  icon?: string;
  displayOrder?: number;
  parentId?: mongoose.Types.ObjectId | string;
  categoryId?: mongoose.Types.ObjectId | string;
}

export enum DomainValueLevel {
  SYSTEM_META = 'SYSTEM_META',
  SYSTEM_DATA = 'SYSTEM_DATA',
  PROPERTY_META = 'PROPERTY_META',
  PROPERTY_DATA = 'PROPERTY_DATA',
}

const DomainValueSchema: Schema = new Schema(
  {
    propertyId: { type: String, required: true, ref: 'Property' },
    category: { type: String, required: false },
    level: { type: String, required: true, enum: DomainValueLevel },
    code: { type: String, required: true },
    name: { type: String, required: true },
    description: { type: String, required: false },
    value: { type: String, required: false, default: '' },
    icon: { type: String, required: false, default: '' },
    displayOrder: { type: Number, required: false },
    categoryId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: 'DomainValue',
    },
    parentId: { type: String, required: false, ref: 'DomainValue' },
  },
  {
    timestamps: true,
  },
);

DomainValueSchema.index(
  { propertyId: 1, categoryId: 1, level: 1, parentId: 1, name: 1 },
  { unique: true },
);

export default mongoose.model<IDomainValue>(
  'DomainValue',
  DomainValueSchema,
  'DomainValue',
);
