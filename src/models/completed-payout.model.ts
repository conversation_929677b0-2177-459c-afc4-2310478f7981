import mongoose, { Schema, Document, Types } from 'mongoose';

export enum PaymentModeEnum {
  CASH = 'cash',
  CHEQUE = 'cheque',
  ONLINE = 'online',
  BANK_TRANSFER = 'bank_transfer',
}

export interface ICompletedPayouts extends Document {
  propertyId: Types.ObjectId;
  netPayout: number;
  date: Date;
  payoutUntil: Date;
  mode: PaymentModeEnum;
  referenceNumber: string;
  attachment: string[];
}

const CompletedPayoutSchema: Schema = new Schema<ICompletedPayouts>(
  {
    propertyId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'Property',
    },
    netPayout: { type: Number, required: true },
    date: { type: Date, required: true },
    payoutUntil: { type: Date, required: true },
    mode: {
      type: String,
      required: true,
      enum: Object.values(PaymentModeEnum),
    },
    referenceNumber: { type: String, required: true },
    attachment: { type: [String], required: false },
  },
  {
    timestamps: true,
  },
);

export default mongoose.model<ICompletedPayouts>(
  'CompletedPayouts',
  CompletedPayoutSchema,
  'CompletedPayouts',
);
