import mongoose, { Schema, Document } from 'mongoose';

export interface IRoomAvailability extends Document {
  propertyId: Schema.Types.ObjectId;
  roomType: Schema.Types.ObjectId;
  dateTime: Date;
  availability: number;
}

const RoomAvailabilitySchema: Schema = new Schema<IRoomAvailability>(
  {
    propertyId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'Property',
    },
    dateTime: { type: Date, required: true },
    roomType: { type: Schema.Types.ObjectId, required: true, ref: 'RoomType' },
    availability: { type: Number, required: true },
  },
  {
    timestamps: true,
  },
);

RoomAvailabilitySchema.index(
  { propertyId: 1, roomType: 1, dateTime: 1 },
  { unique: true },
);

export default mongoose.model<IRoomAvailability>(
  'RoomAvailability',
  RoomAvailabilitySchema,
  'RoomAvailability',
);
