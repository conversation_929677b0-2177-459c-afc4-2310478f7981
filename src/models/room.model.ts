import mongoose, { Schema, Document } from 'mongoose';

export interface IRoom extends Document {
  code: string;
  name: string;
  description: string;
}

const RoomSchema: Schema = new Schema(
  {
    code: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    description: { type: String, required: false },
  },
  {
    timestamps: true,
  },
);

export default mongoose.model<IRoom>('Room', RoomSchema, 'Room');
