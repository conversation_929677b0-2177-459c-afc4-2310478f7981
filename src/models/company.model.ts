import mongoose, { Schema, Document, Types } from 'mongoose';
import Sequence from './sequence.model';

export interface IPhone extends Document {
  countryCode: string;
  phoneNumber: string;
}

export interface IContact extends Document {
  firstName: string;
  lastName: string;
  email: string;
  phone: IPhone;
}

export interface IAddress extends Document {
  address1: string;
  address2: string;
  city: string;
  state: string;
  country: string;
  zipcode: string;
  latitude: number;
  longitude: number;
  locationId?: Types.ObjectId;
  placeId?: string;
}

export interface ITax extends Document {
  pan: string;
  tan: string;
  gstn: string;
  iata: string;
}

export interface ICompany extends Document {
  name: string;
  description: string;
  code: string;
  website: string;
  logoUrl: string;
  email: string;
  fax: string;
  address: IAddress;
  phone: IPhone;
  poc: IContact;
}

export const phoneSchema = new Schema<IPhone>(
  {
    countryCode: { type: String, required: false, default: '+91' },
    phoneNumber: { type: String, required: false, default: '9999999999' },
  },
  { _id: false },
);

export const addressSchema = new Schema<IAddress>(
  {
    address1: { type: String, required: true },
    address2: { type: String, required: false },
    city: { type: String, required: true },
    state: { type: String, required: true },
    country: { type: String, required: true },
    zipcode: { type: String, required: false },
    latitude: { type: Number, required: false },
    longitude: { type: Number, required: false },
    locationId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: 'Location',
    },
    placeId: { type: String, required: false },
  },
  { _id: false },
);

export const contactSchema = new Schema<IContact>(
  {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    email: { type: String, required: true },
    phone: { type: phoneSchema, required: true },
  },
  { _id: false },
);

const companySchema: Schema = new Schema(
  {
    name: { type: String, required: true, unique: true, index: true },
    description: { type: String, required: false },
    code: { type: String, required: false, unique: true, index: true },
    website: { type: String, required: false },
    logoUrl: { type: String, required: false },
    email: { type: String, required: false },
    fax: { type: String, required: false },
    phone: { type: phoneSchema, required: true },
    address: { type: addressSchema, required: true },
    poc: { type: contactSchema, required: true },
  },
  {
    timestamps: true,
  },
);

companySchema.pre<ICompany>('save', async function (next) {
  try {
    if (this.isNew) {
      const sequence = await Sequence.findOneAndUpdate(
        { name: 'companyCode' },
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true, setDefaultsOnInsert: true },
      );

      if (!sequence) throw new Error('Failed to generate sequence number');

      this.code = sequence.sequence_value.toString().padStart(4, '0');
    }
    next();
  } catch (err) {
    next(err instanceof Error ? err : new Error(String(err)));
  }
});

export default mongoose.models.Company ||
  mongoose.model<ICompany>('Company', companySchema, 'Company');
