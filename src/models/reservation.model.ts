import mongoose, { Schema, Document, Types } from 'mongoose';
import { addressSchema, IAddress, IPhone, phoneSchema } from './company.model';
import { IDomainValue } from './domain-value.model';
import Sequence from './sequence.model';
import Property from './property.model';
import { PaymentStatusEnum } from './payment.model';

export enum ReservationStatusEnum {
  BLOCKED = 'blocked',
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
}

export enum GenderEnum {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export type IReservationDetails = {
  couponDiscount?: number;
  roomTypeId: Types.ObjectId;
  packageId: Types.ObjectId;
  startDateTime: Date;
  endDateTime: Date;
  noOfAdults: number;
  noOfChildren: number;
  status: ReservationStatusEnum;
  price: number;
  taxes: IDomainValue[];
  tax: number;
  totalAmount: number;
  guestDetails: {
    _id: Types.ObjectId;
    firstName: string;
    lastName: string;
    email: string;
    phone: IPhone;
    address: IAddress;
    gender: GenderEnum;
  }[];
  specialRequest: string;
  flightDetails: {
    number: string;
    from: string;
    to: string;
    arrivalDateTime: Date | null;
    departureDateTime: Date | null;
  };
};

export interface IReservation extends Document {
  reservationCode: string;
  groupReservationId: string;
  propertyId: Types.ObjectId;
  reservations: IReservationDetails[];
  bookerDetails: {
    firstName: string;
    lastName: string;
    address: IAddress;
    email: string;
    phone: IPhone;
  };
  status: ReservationStatusEnum;
  paymentStatus: PaymentStatusEnum;
  refundAmount?: number;
  cancellationReason?: string;
}

const reservationSchema: Schema = new Schema<IReservation>(
  {
    reservationCode: { type: String, required: true },
    groupReservationId: { type: String, required: true, unique: true },
    propertyId: {
      type: Schema.Types.ObjectId,
      ref: 'Property',
      required: true,
    },
    reservations: [
      {
        couponDiscount: { type: Number, required: true, default: 0 },
        roomTypeId: {
          type: Schema.Types.ObjectId,
          ref: 'RoomType',
          required: true,
        },
        packageId: {
          type: Schema.Types.ObjectId,
          ref: 'Package',
          required: true,
        },
        startDateTime: { type: Date, required: true },
        endDateTime: { type: Date, required: true },
        noOfAdults: { type: Number, required: true, default: 1 },
        noOfChildren: { type: Number, required: true, default: 0 },
        status: {
          type: String,
          enum: Object.values(ReservationStatusEnum),
          default: ReservationStatusEnum.PENDING,
        },
        price: { type: Number, required: true },
        taxes: {
          type: [{ type: Schema.Types.ObjectId, ref: 'DomainValue' }],
          required: false,
        },
        tax: { type: Number, required: true },
        totalAmount: { type: Number, required: true },
        guestDetails: [
          {
            firstName: { type: String, required: true },
            lastName: { type: String, required: true },
            email: { type: String, required: false },
            phone: phoneSchema,
            address: addressSchema,
            gender: {
              type: String,
              enum: Object.values(GenderEnum),
              required: true,
              default: GenderEnum.OTHER,
            },
          },
        ],
        specialRequest: { type: String, required: false, default: '' },
        flightDetails: {
          number: { type: String, required: true },
          from: { type: String, required: true },
          to: { type: String, required: true },
          arrivalDateTime: { type: Date, required: false },
          departureDateTime: { type: Date, required: false },
        },
      },
    ],
    bookerDetails: {
      firstName: { type: String, required: true },
      lastName: { type: String, required: true },
      address: addressSchema,
      email: { type: String, required: true },
      phone: phoneSchema,
      gender: {
        type: String,
        enum: Object.values(GenderEnum),
        required: true,
        default: GenderEnum.OTHER,
      },
    },
    status: {
      type: String,
      enum: Object.values(ReservationStatusEnum),
      default: ReservationStatusEnum.PENDING,
      required: true,
    },
    paymentStatus: {
      type: String,
      enum: Object.values(PaymentStatusEnum),
      default: PaymentStatusEnum.PENDING,
      required: true,
    },
    refundAmount: { type: Number, required: false },
    cancellationReason: { type: String, required: false },
  },
  {
    timestamps: true,
  },
);

reservationSchema.pre<IReservation>('save', async function (next) {
  try {
    if (this.isNew) {
      const sequence = await Sequence.findOneAndUpdate(
        { name: 'reservationCode', propertyId: this.propertyId },
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true, setDefaultsOnInsert: true },
      );

      const property = await Property.findById(this.propertyId)
        .populate<{
          address: {
            locationId: Record<string, unknown>;
          };
        }>({
          path: 'customFields.serviceType',
          model: 'DomainValue',
          select: 'code',
        })
        .populate({
          path: 'address.locationId',
          model: 'Location',
          select: 'code',
        });
      const locationCode =
        property?.address?.locationId?.code?.toString().trim().split(' ') || '';
      if (!sequence) throw new Error('Failed to generate sequence number');
      this.reservationCode =
        // @ts-expect-error Any Type Error
        property?.customFields.get('propertyDetails')?.bookingIdPrefix?.trim() +
        '-' +
        // @ts-expect-error Any Type Error
        property?.customFields?.get('serviceType').code +
        '-' +
        locationCode[locationCode.length - 1] +
        '-' +
        new Date().getFullYear().toString().slice(-2) +
        (new Date().getMonth() + 1).toString().padStart(2, '0') +
        new Date().getDate().toString().padStart(2, '0') +
        '-' +
        sequence.sequence_value.toString().padStart(4, '0');
    }
    next();
  } catch (err) {
    next(err instanceof Error ? err : new Error(String(err)));
  }
});

export default mongoose.model<IReservation>(
  'Reservation',
  reservationSchema,
  'Reservation',
);
