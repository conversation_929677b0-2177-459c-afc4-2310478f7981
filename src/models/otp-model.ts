import mongoose, { Schema, Document } from 'mongoose';

export enum OtpPurposeEnum {
  LOGIN = 'login',
  RESET_PASSWORD = 'reset_password',
  VERIFY_EMAIL = 'verify_email',
  TRANSACTION = 'transaction',
  CANCEL_BOOKING = 'cancel_booking',
  SIGN_UP = 'sign_up',
}

export interface IOtp extends Document {
  userEmail: string;
  reservationCode: string;
  purpose: OtpPurposeEnum;
  otp: string;
  createdAt: Date;
  expiresAt: Date;
}

const OTPSchema: Schema = new Schema<IOtp>({
  userEmail: {
    type: String,
    required: true,
    index: true,
  },
  reservationCode: {
    type: String,
    required: false,
  },
  purpose: {
    type: String,
    required: true,
    enum: Object.values(OtpPurposeEnum),
  },
  otp: {
    type: String,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  expiresAt: {
    type: Date,
    required: true,
    index: { expires: '10m' },
  },
});

export default mongoose.model<IOtp>('OTP', OTPSchema, 'OTP');
