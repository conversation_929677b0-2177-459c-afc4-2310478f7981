import mongoose, { Schema, Document } from 'mongoose';
import Sequence from './sequence.model';
import { IPackage } from './package.model';

export interface IRoomType extends Document {
  _id: string;
  active: boolean;
  code: string;
  name: string;
  description: string;
  noOfRooms: number;
  maxOccupancy: number;
  area: number;
  bedType: string;
  imageUrls: string[];
  propertyId: mongoose.Types.ObjectId;
  amenities: mongoose.Types.ObjectId[];
  customFields: Map<string, unknown>;
  packages?: IPackage[];
  bufferTime: number;
}

const roomTypeSchema: Schema = new Schema<IRoomType>(
  {
    code: { type: String, required: false, unique: true },
    name: { type: String, required: true },
    active: { type: Boolean, required: true, default: true },
    description: { type: String, required: false },
    noOfRooms: { type: Number, required: true, default: 1 },
    maxOccupancy: { type: Number, required: true },
    area: { type: Number, required: true },
    bedType: { type: String, required: true },
    imageUrls: { type: [String], required: true },
    propertyId: {
      type: Schema.Types.ObjectId,
      ref: 'Property',
      required: true,
    },
    amenities: { type: [Schema.Types.ObjectId], required: false },
    bufferTime: { type: Number, required: true, default: 0 },
    customFields: {
      type: Map,
      of: Object,
    },
  },
  {
    timestamps: true,
  },
);

roomTypeSchema.index({ code: 1, propertyId: 1 }, { unique: true });
roomTypeSchema.index({ name: 1, propertyId: 1 }, { unique: true });

roomTypeSchema.pre<IRoomType>('save', async function (next) {
  try {
    if (this.isNew) {
      const sequence = await Sequence.findOneAndUpdate(
        { name: 'roomTypeCode', propertyId: this.propertyId },
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true, setDefaultsOnInsert: true },
      );

      if (!sequence) throw new Error('Failed to generate sequence number');

      this.code = sequence.sequence_value.toString().padStart(4, '0');
    }
    next();
  } catch (err) {
    next(err instanceof Error ? err : new Error(String(err)));
  }
});

export default mongoose.model<IRoomType>(
  'RoomType',
  roomTypeSchema,
  'RoomType',
);
