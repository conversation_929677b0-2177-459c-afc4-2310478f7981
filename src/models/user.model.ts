import mongoose, { Schema, Document } from 'mongoose';
import {
  addressSchema,
  IAddress,
  IContact,
  IPhone,
  phoneSchema,
} from './company.model';

export enum UserRoleEnum {
  SUPERADMIN = 'superadmin',
  ADMIN = 'admin',
  USER = 'user',
  MERCHANT = 'merchant',
}

export interface IUser extends Document {
  firstName: string;
  lastName: string;
  email: string;
  userName: string;
  password: string;
  role: UserRoleEnum[];
  phone: IPhone;
  poc: IContact;
  address?: IAddress;
}

const UserSchema = new Schema(
  {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    email: { type: String, required: false, unique: true, sparse: true },
    userName: { type: String, required: true, unique: true },
    password: { type: String, required: false },
    phone: { type: phoneSchema, required: false },
    address: { type: addressSchema, required: false },
    role: {
      type: [String],
      required: true,
      default: UserRoleEnum.USER,
      enum: Object.values(UserRoleEnum),
    },
  },
  {
    timestamps: true,
  },
);

UserSchema.path('email').validate(function (value) {
  if (!value && !this.phone) {
    return false;
  }
  return true;
}, 'Either email or phone must be provided.');

UserSchema.path('phone').validate(function (value) {
  if (!value && !this.email) {
    return false;
  }
  return true;
}, 'Either phone or email must be provided.');

UserSchema.index(
  { email: 1, phone: 1, userName: 1 },
  { unique: true, sparse: true },
);

export default mongoose.model<IUser>('User', UserSchema, 'User');
