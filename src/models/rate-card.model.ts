import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IRateCard extends Document {
  name: string;
  description: string;
  propertyId: Types.ObjectId;
  dateTime: Date;
  packageId: Types.ObjectId;
  price: number;
}

const RateCardSchema: Schema = new Schema<IRateCard>(
  {
    name: { type: String, required: true },
    description: { type: String, required: false },
    propertyId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'Property',
    },
    dateTime: { type: Date, required: true },
    packageId: { type: Schema.Types.ObjectId, required: true, ref: 'Package' },
    price: { type: Number, required: true },
  },
  {
    timestamps: true,
  },
);

RateCardSchema.index(
  { propertyId: 1, packageId: 1, dateTime: 1 },
  { unique: true },
);

export default mongoose.model<IRateCard>(
  'RateCard',
  RateCardSchema,
  'RateCard',
);
