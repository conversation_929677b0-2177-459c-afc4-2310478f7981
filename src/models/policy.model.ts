import mongoose, { Schema, Document } from 'mongoose';

export interface IPolicy extends Document {
  name: string;
  serviceType?: string;
  order: number;
  description?: string;
  category?: string;
  input_type: 'text' | 'radio' | 'checkbox' | 'select' | 'custom_rule';
  options?: Array<{ value: string; description: string }>;
  is_required: boolean;
  validation_rules?: {
    min?: number;
    max?: number;
    pattern?: string;
    required?: boolean;
  };
  conditional_logic?: {
    field: string;
    value: unknown;
    operator:
      | 'equals'
      | 'not_equals'
      | 'contains'
      | 'greater_than'
      | 'less_than';
  };
  config?: {
    rules?: Array<{
      refund_percent?: number;
      before_hours?: number;
      after_hours?: number;
      [key: string]: unknown;
    }>;
    [key: string]: unknown;
  };
  propertyId?: string;
  deleted: boolean;
}

const PolicySchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    serviceType: {
      type: Schema.Types.ObjectId,
      required: false,
      trim: true,
      ref: 'DomainValue',
    },
    order: {
      type: Number,
      required: true,
      default: 0,
    },
    description: {
      type: String,
      required: false,
      trim: true,
    },
    category: {
      type: Schema.Types.ObjectId,
      required: false,
      trim: true,
      ref: 'DomainValue',
    },
    input_type: {
      type: String,
      required: true,
      enum: ['text', 'radio', 'checkbox', 'select', 'custom_rule'],
    },
    options: [
      {
        value: { type: String, required: true, trim: true },
        description: { type: String, required: false, trim: true },
      },
    ],
    is_required: {
      type: Boolean,
      required: true,
      default: false,
    },
    validation_rules: {
      min: { type: Number },
      max: { type: Number },
      pattern: { type: String },
      required: { type: Boolean, default: false },
    },
    conditional_logic: {
      field: { type: String },
      value: { type: Schema.Types.Mixed },
      operator: {
        type: String,
        enum: ['equals', 'not_equals', 'contains', 'greater_than', 'less_than'],
      },
    },
    config: {
      type: Schema.Types.Mixed,
      default: {},
    },
    propertyId: {
      type: Schema.Types.ObjectId,
      ref: 'Property',
      required: false,
    },
    deleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  },
);

// Index for better query performance
PolicySchema.index({ propertyId: 1, deleted: 1 });
PolicySchema.index({ input_type: 1, deleted: 1 });
PolicySchema.index({ category: 1, propertyId: 1, deleted: 1 });

export default mongoose.model<IPolicy>('Policy', PolicySchema, 'Policy');
