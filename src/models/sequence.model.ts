import mongoose, { Schema, Document } from 'mongoose';

export interface ISequence extends Document {
  _id: string;
  sequence_value: number;
}

const sequenceSchema = new Schema({
  name: { type: String, required: true },
  sequence_value: { type: Number, default: 0 },
  companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: false },
  propertyId: { type: Schema.Types.ObjectId, ref: 'Property', required: false },
});

export default mongoose.model<ISequence>(
  'Sequence',
  sequenceSchema,
  'Sequence',
);
