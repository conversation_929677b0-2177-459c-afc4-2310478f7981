import mongoose, { Schema, Types } from 'mongoose';

export interface UserTokenDocument extends Document {
  userId: Types.ObjectId;
  token: string;
}

export const userTokenDocument: Schema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  token: { type: String, required: true },
});

export default mongoose.model<UserTokenDocument>(
  'UserToken',
  userTokenDocument,
  'UserToken',
);
