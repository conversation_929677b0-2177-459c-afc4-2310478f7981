import mongoose, { Schema, Document } from 'mongoose';
import {
  addressSchema,
  contactSchema,
  IAddress,
  IContact,
  IPhone,
  phoneSchema,
} from './company.model';
import Sequence from './sequence.model';
import { IPackage } from './package.model';

export interface IProperty extends Document {
  _id: string;
  active: boolean;
  deleted: boolean;
  companyId: string;
  name: string;
  description: string;
  code: string;
  website: string;
  logoUrl: string;
  email: string;
  fax: string;
  address: IAddress;
  phone: IPhone;
  poc: IContact;
  packages?: IPackage[];
  status: PropertyStatus;
  customFields: Record<string, unknown>;
}

export enum PropertyStatus {
  PENDING = 'Pending',
  APPROVED = 'Approved',
  REJECTED = 'Rejected',
}

const propertySchema: Schema = new Schema(
  {
    active: { type: Boolean, required: true, default: false },
    deleted: { type: Boolean, required: true, default: false },
    name: { type: String, required: true },
    description: { type: String, required: false },
    code: { type: String, required: false, unique: true },
    premisesType: {
      type: String,
      required: false,
      default: 'Airside',
      enum: ['Airside', 'Landside'],
    },
    website: { type: String },
    logoUrl: { type: String },
    email: { type: String },
    fax: { type: String },
    phone: { type: phoneSchema, required: true },
    address: { type: addressSchema, required: true },
    poc: { type: contactSchema, required: true },
    status: {
      type: String,
      required: true,
      default: PropertyStatus.PENDING,
      enum: Object.values(PropertyStatus),
    },
    customFields: {
      type: Map,
      of: Object,
    },
  },
  {
    timestamps: true,
  },
);

propertySchema.pre<IProperty>('save', async function (next) {
  try {
    if (this.isNew) {
      const sequence = await Sequence.findOneAndUpdate(
        { name: 'propertyCode' },
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true, setDefaultsOnInsert: true },
      );

      if (!sequence) throw new Error('Failed to generate sequence number');

      this.code = sequence.sequence_value.toString().padStart(4, '0');
    }
    next();
  } catch (err) {
    next(err instanceof Error ? err : new Error(String(err)));
  }
});

export default mongoose.model<IProperty>(
  'Property',
  propertySchema,
  'Property',
);
