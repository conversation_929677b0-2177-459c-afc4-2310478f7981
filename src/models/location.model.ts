import mongoose, { Schema, Document } from 'mongoose';

export interface ILocation extends Document {
  code: string;
  name: string;
  description: string;
  latitude: number;
  longitude: number;
  city: string;
  country: string;
  deleted: boolean;
  _id: string;
}

const LocationSchema: Schema = new Schema(
  {
    code: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    description: { type: String, required: false },
    latitude: { type: Number, required: true },
    longitude: { type: Number, required: true },
    city: { type: String, required: true },
    country: { type: String, required: true },
    deleted: { type: Boolean, required: false, default: false },
  },
  {
    timestamps: true,
  },
);

export default mongoose.model<ILocation>(
  'Location',
  LocationSchema,
  'Location',
);
