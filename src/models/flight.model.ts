import mongoose, { Schema, Document } from 'mongoose';

export interface IFlight extends Document {
  code: string;
  name: string;
  description: string;
}

const FlightSchema: Schema = new Schema(
  {
    code: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
  },
  {
    timestamps: true,
  },
);

export default mongoose.model<IFlight>('Flight', FlightSchema, 'Flight');
