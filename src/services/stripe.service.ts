import Stripe from 'stripe';
import { IPayment } from '../models/payment.model';
import { FRONTEND_DOMAIN_URL, STRIPE_SECRET_KEY } from '../constants';
import { IReservation } from '../models/reservation.model';
import { IPackage } from '../models/package.model';

interface LineItem {
  price_data: {
    currency: string;
    product_data: {
      name: string;
      description: string;
    };
    unit_amount: number;
  };
  quantity: number;
}

function getFinalAmount(amount: number) {
  return Number((amount * 100).toFixed(0));
}

export const createCheckoutSession = async (
  currency: string,
  payment: IPayment,
  reservation: IReservation,
) => {
  const stripe = new Stripe(STRIPE_SECRET_KEY);

  const YOUR_DOMAIN = FRONTEND_DOMAIN_URL;

  const lineItems: LineItem[] = Object.values(
    reservation.reservations.reduce((acc: Record<string, LineItem>, res) => {
      const packageData = res.packageId as unknown as IPackage;
      const key = packageData.name;
      if (acc[key]) {
        acc[key].quantity += 1;
      } else {
        acc[key] = {
          price_data: {
            currency: currency,
            product_data: {
              name: packageData.name,
              description: `Payment for ${packageData.name}`,
            },
            unit_amount: getFinalAmount(res.totalAmount),
          },
          quantity: 1,
        };
      }
      return acc;
    }, {}),
  );

  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: lineItems,
    mode: 'payment',
    success_url: `${YOUR_DOMAIN}/reservation/success?paymentId=${payment._id}`,
    cancel_url: `${YOUR_DOMAIN}/reservation/failed?paymentId=${payment._id}`,
  });

  return {
    sessionId: session.id,
    sessionUrl: session.url || '',
  };
};

export const createStripeRefund = async (sessionId: string, amount: number) => {
  const secretKey = STRIPE_SECRET_KEY;

  if (amount <= 0) {
    throw new Error('Amount should be greater than 0');
  }

  const stripe = new Stripe(secretKey);

  const session = await stripe.checkout.sessions.retrieve(sessionId);
  const paymentIntentId = session.payment_intent;

  if (!paymentIntentId) {
    throw new Error('No payment intent found for this checkout session');
  }

  // const payment = await stripe.paymentIntents.retrieve(paymentIntentId);
  // if (payment.status !== 'succeeded') {
  //   throw new Error('Payment has not succeeded');
  // }

  return await stripe.refunds.create({
    payment_intent: paymentIntentId.toString(),
    amount: getFinalAmount(amount),
  });
};

export const getSessionDetails = async (sessionId: string) => {
  const stripe = new Stripe(STRIPE_SECRET_KEY);
  return await stripe.checkout.sessions.retrieve(sessionId);
};
