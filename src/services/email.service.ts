import nodemailer from 'nodemailer';
import logger from '../utils/logger';
import { IReservation } from '../models/reservation.model';
import { IProperty } from '../models/property.model';
import {
  cancelBookingGuestTemplate,
  cancelBookingMerchantTemplate,
  reservationTemplate,
  updateBookingHostTemplate,
  updateReservationGuestTemplate,
} from '../templates/reservation';
import { approvalTemplate } from '../templates/approval.template';
import { activeStatusTemplate } from '../templates/active-status.template';
import { forgotPasswordTemplate } from '../templates/forgot-password.template';
import { otpVerificationTemplate } from '../templates/otp-verification.template';
import {
  MAIL_IS_SECURE,
  MAIL_PASSWORD,
  MAIL_SMTP_HOST,
  MAIL_SMTP_PORT,
  MAIL_USERNAME,
} from '../constants';

export class EmailService {
  constructor() {}

  async sendConfirmationEmail(to: string, firstName: string): Promise<void> {
    try {
      const html = `
          <h1>Welcome to StayTransit, ${firstName}!</h1>
          <p>Your account has been successfully created.</p>
          <p>Thank you for joining us!</p>
        `;
      await this.sendMail(
        to,
        'Welcome to StayTransit - Account Confirmation',
        html,
      );

      logger.info(`Confirmation email sent to ${to}`);
    } catch (error) {
      logger.error(
        `Failed to send confirmation email to ${to}: ${(error as Error).message}`,
      );
      throw error;
    }
  }

  async sendReservationConfirmationEmail(
    to: string,
    firstName: string,
    reservation: IReservation,
    property: IProperty,
  ): Promise<void> {
    try {
      const html = reservationTemplate(firstName, reservation, property);

      await this.sendMail(to, 'StayTransit - Reservation Confirmation', html);
      logger.info(
        `Reservation confirmation email sent to ${to} for reservation ${reservation.groupReservationId}`,
      );
    } catch (error) {
      logger.error(
        `Failed to send reservation confirmation email to ${to}: ${(error as Error).message}`,
      );
      throw error;
    }
  }

  async sendApprovalStatusEmail(
    to: string,
    firstName: string,
    property: IProperty,
    status: 'Approved' | 'Rejected' | 'Pending',
  ): Promise<void> {
    try {
      const html = approvalTemplate(firstName, status);
      const subject =
        status === 'Approved'
          ? 'StayTransit - Property Approved!'
          : 'StayTransit - Property Application Update';

      await this.sendMail(to, subject, html);
      logger.info(
        `Approval status email sent to ${to} for property ${property.name} - Status: ${status}`,
      );
    } catch (error) {
      logger.error(
        `Failed to send approval status email to ${to}: ${(error as Error).message}`,
      );
      throw error;
    }
  }

  async sendActiveStatusEmail(
    to: string,
    firstName: string,
    property: IProperty,
    isActive: boolean,
  ): Promise<void> {
    try {
      const html = activeStatusTemplate(firstName, isActive);
      const subject = isActive
        ? 'StayTransit - Property Activated!'
        : 'StayTransit - Property Deactivated';

      await this.sendMail(to, subject, html);
      logger.info(
        `Active status email sent to ${to} for property ${property.name} - Active: ${isActive}`,
      );
    } catch (error) {
      logger.error(
        `Failed to send active status email to ${to}: ${(error as Error).message}`,
      );
      throw error;
    }
  }

  async sendForgotPasswordEmail(
    to: string,
    firstName: string,
    token: string,
  ): Promise<void> {
    try {
      const html = forgotPasswordTemplate(firstName, token);

      await this.sendMail(to, 'StayTransit - Password Reset Request', html);

      logger.info(`Password reset email sent to ${to}`);
    } catch (error) {
      logger.error(
        `Failed to send password reset email to ${to}: ${(error as Error).message}`,
      );
      throw error;
    }
  }

  async sendCancellationEmail(
    to: string,
    reservation: IReservation,
    property: IProperty,
    sendToMerchant: boolean = true,
  ): Promise<void> {
    try {
      const guestHtml = cancelBookingGuestTemplate(property, reservation);
      await this.sendMail(
        to,
        `Reservation Cancellation - ${reservation.reservationCode}`,
        guestHtml,
      );
      if (sendToMerchant) {
        const merchantHtml = cancelBookingMerchantTemplate(
          property,
          reservation,
        );
        await this.sendMail(
          property.poc.email,
          `Reservation Cancellation - ${reservation.reservationCode}`,
          merchantHtml,
        );
      }
      logger.info(
        `Cancellation email sent to ${to} for reservation ${reservation.groupReservationId}`,
      );
    } catch (error) {
      logger.error(
        `Failed to send cancellation email to ${to}: ${(error as Error).message}`,
      );
      throw error;
    }
  }

  async sendOtpVerificationEmail(
    to: string,
    firstName: string,
    otp: string,
  ): Promise<void> {
    try {
      const html = otpVerificationTemplate(firstName, otp);
      await this.sendMail(to, 'StayTransit - OTP Verification', html);

      logger.info(`OTP verification email sent to ${to}`);
    } catch (error) {
      logger.error(
        `Failed to send OTP verification email to ${to}: ${(error as Error).message}`,
      );
    }
  }

  async sendUpdateReservationEmail(
    to: string,
    reservation: IReservation,
    property: IProperty,
    sendToMerchant: boolean = true,
  ): Promise<void> {
    try {
      const html = updateReservationGuestTemplate(reservation, property);
      await this.sendMail(
        to,
        `Reservation Updated - ${reservation.reservationCode}`,
        html,
      );
      logger.info(
        `Reservation update email sent to ${to} for reservation ${reservation.groupReservationId}`,
      );
      if (sendToMerchant) {
        const merchantHtml = updateBookingHostTemplate(property, reservation);
        await this.sendMail(
          property.poc.email,
          `Reservation Updated -  ${reservation.reservationCode}`,
          merchantHtml,
        );
      }
      logger.info(
        `Reservation update email sent to ${property.poc.email} for reservation ${reservation.groupReservationId}`,
      );
    } catch (error) {
      logger.error(
        `Failed to send reservation update email: ${(error as Error).message}`,
      );
      throw error;
    }
  }

  async sendMail(to: string, subject: string, html: string): Promise<void> {
    try {
      const transporter = nodemailer.createTransport({
        host: MAIL_SMTP_HOST,
        port: MAIL_SMTP_PORT,
        secure: MAIL_IS_SECURE,
        requireTLS: MAIL_SMTP_PORT === 587,
        auth: {
          user: MAIL_USERNAME,
          pass: MAIL_PASSWORD,
        },
      } as nodemailer.TransportOptions);

      await transporter.sendMail({
        from: `"StayTransit" <${MAIL_USERNAME}>`,
        to: to,
        subject: subject,
        html: html,
      });
      logger.info(`Email sent to ${to}`);
    } catch (error) {
      logger.error(
        `Failed to send email to ${to}: ${(error as Error).message}`,
      );
      throw error;
    }
  }
}
