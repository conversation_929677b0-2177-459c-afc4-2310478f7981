import { IProperty } from '../../models/property.model';
import { IReservation } from '../../models/reservation.model';
import { formatCurrency, formatDate, formatTime } from './utils';
import { FRONTEND_DOMAIN_URL } from '../../constants';

export const cancelBookingMerchantTemplate = (
  property: IProperty,
  reservation: IReservation,
) => {
  const domainUrl = FRONTEND_DOMAIN_URL;
  const checkIn = new Date(reservation.reservations[0].startDateTime);
  const checkOut = new Date(reservation.reservations[0].endDateTime);

  return `
        <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Booking Cancellation Notice</title>
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f6f7fb; margin: 0; padding: 0; color: #222; }
    .header-bar { background: #fc4f4b; color: #fff; padding: 28px 0 18px 0; text-align: center; }
    .logo { display: inline-block; font-weight: bold; font-size: 28px; color: #fff; letter-spacing: 1px; margin-bottom: 6px; }
    .confirmation { font-size: 22px; font-weight: 700; margin-top: 8px; color: #fff; }
    .main-content { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 32px 32px 0 32px; position: relative; top: -24px; }
    .section-title { font-size: 18px; font-weight: 700; margin: 24px 0 12px 0; color: #222; }
    .booking-id { font-size: 13px; color: #888; margin-bottom: 8px; }
    .property-name { font-size: 16px; font-weight: 600; color: #fc4f4b; margin-bottom: 2px; }
    .details { background: #f6f7fb; border-radius: 8px; padding: 16px; margin: 24px 0; }
    .details p { margin: 5px 0; }
    .details strong { color: #fc4f4b; }
    .button { background: #fc4f4b; text-decoration: none; color: #fff; border: none; border-radius: 6px; padding: 10px 24px; font-size: 15px; font-weight: 700; cursor: pointer; margin-top: 10px; display: inline-block; text-decoration: none; }
    .button:hover { background: #fc4f4b; }
    .footer { background: #f6f7fb; padding: 15px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #eee; }
    .footer a { color: #fc4f4b; text-decoration: none; }
    @media (max-width: 700px) {
      .main-content { padding: 12px !important; }
    }
  </style>
</head>
<body>
  <div class="header-bar">
    <div class="logo">StayTransit</div>
    <div class="confirmation">Booking Cancellation Notice</div>
  </div>
  <div class="main-content">
    <div class="section-title">Cancellation Details <span class="booking-id">(ID: ${reservation.reservationCode})</span></div>
    <div class="property-name">${property.name || 'Hotel Name'}</div>
    <div class="details">
      <p><strong>Primary Guest Information:</strong></p>
      <p>&bull; Name: ${reservation.reservations[0].guestDetails[0].firstName} ${reservation.reservations[0].guestDetails[0].lastName}</p>
      <p>&bull; Email: ${reservation.reservations[0].guestDetails[0].email}</p>
    </div>
    <div class="details">
      <p><strong>Cancelled Booking Details:</strong></p>
      <p>&bull; Booking ID: ${reservation.reservationCode}</p>
      <p>&bull; Check-in: ${formatDate(checkIn)} at ${formatTime(checkIn)}</p>
      <p>&bull; Check-out: ${formatDate(checkOut)} at ${formatTime(checkOut)}</p>
    </div>
    <p>We regret to inform you that a booking has been cancelled by the guest on StayTransit. Please update your availability in the StayTransit Partner Panel.</p>
    <a href="${domainUrl}" class="button">Access Partner Panel</a>
    <p>For any questions, our support team is here to assist you.<br>Best regards,<br>StayTransit Team</p>
    <div class="footer">
      © 2025 StayTransit. All rights reserved. <a href="https://staytransit.com/support">Contact Support</a>
    </div>
  </div>
</body>
</html>
    `;
};

export const cancelBookingGuestTemplate = (
  property: IProperty,
  reservation: IReservation,
) => {
  const checkIn = new Date(reservation.reservations[0].startDateTime);
  const checkOut = new Date(reservation.reservations[0].endDateTime);
  Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
  const roomCharges = reservation.reservations.reduce(
    (sum, res) => sum + res.price,
    0,
  );
  const couponDiscount = reservation.reservations.reduce(
    (sum, res) => sum + ((res.couponDiscount as number) || 0),
    0,
  );
  const taxesAndCharges = reservation.reservations.reduce(
    (sum, res) => sum + res.tax,
    0,
  );
  const totalPaid = reservation.reservations.reduce(
    (sum, res) => sum + res.totalAmount,
    0,
  );

  return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reservation Cancellation - StayTransit</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f6f7fb; margin: 0; padding: 0; color: #222; }
                .header-bar { background: #fc4f4b; color: #fff; padding: 28px 0 18px 0; text-align: center; }
                .logo { display: inline-block; font-weight: bold; font-size: 28px; color: #fff; letter-spacing: 1px; margin-bottom: 6px; }
                .confirmation { font-size: 22px; font-weight: 700; margin-top: 8px; color: #fff; }
                .greeting { font-size: 15px; color: #fff; margin-top: 4px; }
                .main-content { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 32px 32px 0 32px; position: relative; top: -24px; }
                .section-title { font-size: 18px; font-weight: 700; margin: 24px 0 12px 0; color: #222; }
                .booking-id { font-size: 13px; color: #888; margin-bottom: 8px; }
                .property-name { font-size: 16px; font-weight: 600; color: #fc4f4b; margin-bottom: 2px; }
                .property-address { font-size: 15px; color: #222; margin-bottom: 4px; word-break: break-word; }
                .directions-link { color: #fc4f4b; font-size: 14px; text-decoration: underline; margin-bottom: 10px; display: inline-block; }
                .help-links { font-size: 13px; color: #555; margin-bottom: 10px; }
                .help-links a { color: #fc4f4b; text-decoration: underline; margin-left: 8px; }
                .details-list { margin: 0 0 12px 0; padding: 0; list-style: none; }
                .details-list li { margin-bottom: 6px; font-size: 15px; }
                .details-action { color: #fc4f4b; text-decoration: underline; font-size: 14px; margin-left: 8px; cursor: pointer; }
                .support-section { background: #f6f7fb; border-radius: 8px; padding: 16px; margin: 24px 0; display: flex; align-items: center; justify-content: space-between; }
                .support-title { font-size: 15px; font-weight: 600; }
                .support-btn { background: #fc4f4b; color: #fff; border: none; border-radius: 6px; padding: 8px 18px; font-size: 14px; font-weight: 600; cursor: pointer; }
                .payment-table { width: 100%; border-collapse: collapse; margin: 18px 0; }
                .payment-table td { padding: 10px 0; font-size: 15px; border-bottom: 1px solid #f0f0f0; }
                .payment-table .label { color: #555; }
                .payment-table .total { font-weight: bold; color: #222; font-size: 17px; border-top: 2px solid #fc4f4b; }
                .pay-btn { background: #fc4f4b; color: #fff; border: none; border-radius: 6px; padding: 10px 28px; font-size: 16px; font-weight: 700; cursor: pointer; margin-top: 10px; }
                .cancel-section { background: #f6f7fb; border-radius: 8px; padding: 18px 20px; margin: 24px 0; }
                .cancel-title { font-size: 15px; font-weight: 700; color: #FF715D; margin-bottom: 6px; }
                .cancel-policy { font-size: 14px; color: #555; margin-bottom: 8px; }
                .cancel-btn { background: #fff; border: 1.5px solid #fc4f4b; color: #fc4f4b; padding: 10px 24px; border-radius: 8px; font-size: 15px; font-weight: 700; cursor: pointer; margin-top: 10px; display: inline-block; }
                .rules-section { margin: 24px 0; }
                .rules-title { font-size: 15px; font-weight: 700; color: #222; margin-bottom: 8px; }
                .rules-list { margin: 0 0 0 18px; padding: 0; color: #444; font-size: 14px; }
                @media (max-width: 700px) {
                    .main-content { padding: 12px !important; }
                }
            </style>
        </head>
        <body>
            <div class="header-bar">
                <div class="logo">StayTransit</div>
                <div class="confirmation">Your reservation has been cancelled.</div>
                <div class="greeting">Hello ${reservation.bookerDetails.firstName}, We regret to inform you about the cancellation.</div>
            </div>
            <div class="main-content">
                <div class="section-title">Booking details <span class="booking-id">(ID: ${reservation.reservationCode})</span></div>
                <div class="property-name">${property.name}</div>
                <div class="details">
                    <p><strong>Booking ID:</strong> ${reservation.reservationCode}</p>
                    <p><strong>Check-in:</strong> ${formatDate(checkIn)} at ${formatTime(checkIn)}</p>
                    <p><strong>Check-out:</strong> ${formatDate(checkOut)} at ${formatTime(checkOut)}</p>
                    <p><strong>No. of Guests:</strong> ${reservation.reservations[0]?.noOfAdults || 1} Adult</p>
                    <p><strong>No. of Rooms:</strong> ${reservation.reservations.length} ${reservation.reservations.length === 1 ? 'Room' : 'Rooms'}</p>                <div class="details">
                    <p><strong>Payment Summary</strong></p>
                    <table class="payment-table">
                        <tr><td class="label">Room Charges</td><td style="text-align:right;">${formatCurrency(roomCharges)}</td></tr>
                        <tr><td class="label">Coupon Discount</td><td style="text-align:right;">${formatCurrency(couponDiscount)}</td></tr>
                        <tr><td class="label">Taxes & Charges</td><td style="text-align:right;">${formatCurrency(taxesAndCharges)}</td></tr>
                        <tr><td class="label total">Total</td><td style="text-align:right;">${formatCurrency(totalPaid)}</td></tr>
                    </table>
                </div>
                <p>Your booking has been cancelled. We're listing the details for your cancellation below. It's sad we won't be seeing you. We hope to see you in the future.</p>
                <div class="footer">
                    © 2025 StayTransit. All rights reserved. <a href="https://staytransit.com/support">Contact Support</a> | <a href="https://staytransit.com/privacy">Privacy Policy</a>
                </div>
            </div>
        </body>
        </html>
    `;
};
