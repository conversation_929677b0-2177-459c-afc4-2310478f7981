import { IProperty } from '../../models/property.model';
import { IReservation } from '../../models/reservation.model';
import { IPolicy } from '../../models/policy.model';
import { formatCurrency, formatDate, formatTime } from './utils';
import { FRONTEND_DOMAIN_URL } from '../../constants';
import { IRoomType } from '../../models/room-type.model';
import { IPackage } from '../../models/package.model';

export const updateBookingHostTemplate = (
  property: IProperty,
  reservation: IReservation,
) => {
  const checkIn = new Date(reservation.reservations[0].startDateTime);
  const checkOut = new Date(reservation.reservations[0].endDateTime);

  return `
        <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Booking Update Notice</title>
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f6f7fb; margin: 0; padding: 0; color: #222; }
    .header-bar { background: #fc4f4b; color: #fff; padding: 28px 0 18px 0; text-align: center; }
    .logo { display: inline-block; font-weight: bold; font-size: 28px; color: #fff; letter-spacing: 1px; margin-bottom: 6px; }
    .confirmation { font-size: 22px; font-weight: 700; margin-top: 8px; color: #fff; }
    .main-content { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 32px 32px 0 32px; position: relative; top: -24px; }
    .section-title { font-size: 18px; font-weight: 700; margin: 24px 0 12px 0; color: #222; }
    .booking-id { font-size: 13px; color: #888; margin-bottom: 8px; }
    .property-name { font-size: 16px; font-weight: 600; color: #fc4f4b; margin-bottom: 2px; }
    .details { background: #f6f7fb; border-radius: 8px; padding: 16px; margin: 24px 0; }
    .details p { margin: 5px 0; }
    .details strong { color: #fc4f4b; }
    .footer { background: #f6f7fb; padding: 15px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #eee; }
    .footer a { color: #fc4f4b; text-decoration: none; }
    @media (max-width: 700px) {
      .main-content { padding: 12px !important; }
    }
  </style>
</head>
<body>
  <div class="header-bar">
    <div class="logo">StayTransit</div>
    <div class="confirmation">Booking Update Notice</div>
  </div>
  <div class="main-content">
    <div class="section-title">Booking Update Details <span class="booking-id">(ID: ${reservation.reservationCode})</span></div>
    <div class="property-name">${property.name}</div>
     <div class="details">
      <p><strong>Updated Guest Information:</strong></p>
      ${reservation.reservations
        .map((res) => {
          return `<p>&bull; Name: ${res.guestDetails
            .map((guest) => `${guest.firstName} ${guest.lastName}`)
            .join(', ')}</p>`;
        })
        .join('')}
    </div>
    <div class="details">
      <p><strong>Booking Details:</strong></p>
      <p>&bull; Booking ID: ${reservation.reservationCode}</p>
      <p>&bull; Check-in: ${formatDate(checkIn)} at ${formatTime(checkIn)}</p>
      <p>&bull; Check-out: ${formatDate(checkOut)} at ${formatTime(checkOut)}</p>
    </div>
    <p>Please check the Partner Panel for more information or changes related to this booking.</p>
    <p>Thanks for your continued support,<br>StayTransit Team</p>
    <div class="footer">
      © 2025 StayTransit. All rights reserved. <a href="https://staytransit.com/support">Contact Support</a>
    </div>
  </div>
</body>
</html>
    `;
};

export const updateReservationGuestTemplate = (
  reservation: IReservation,
  property: IProperty,
) => {
  const domainUrl = FRONTEND_DOMAIN_URL;
  const updateUrl = `${domainUrl}/reservation/verify?type=update`;
  const cancelUrl = `${domainUrl}/reservation/verify?type=cancel`;

  const totalCouponDiscount = reservation.reservations.reduce(
    (sum, res) => sum + (res.couponDiscount || 0),
    0,
  );

  // @ts-ignore
  const cancellationPolicies = property.customFields.get('cancellationPolicy');

  const address = [
    property.address.address1,
    property.address.address2,
    property.address.city,
    property.address.state,
    property.address.country,
    property.address.zipcode,
  ]
    .filter(Boolean)
    .join(', ')
    .replace(',,', ',');

  const mapUrl = `https://www.google.com/maps/search/?api=1&query=${property.address.latitude}%2C${property.address.longitude}&query_place_id=${property.address.placeId}`;

  let policiesHtml =
    '<div style="color: #888; font-size: 14px;">No property rules listed.</div>';
  if (
    cancellationPolicies &&
    Array.isArray(cancellationPolicies) &&
    cancellationPolicies.length > 0
  ) {
    const items = cancellationPolicies
      .map((policy: IPolicy) => {
        return `<li>${policy.description}</li>`;
      })
      .filter(Boolean);
    if (items.length > 0) {
      policiesHtml = `<ul style="margin: 8px 0 0 18px; padding: 0; color: #444; font-size: 14px;">${items.join('')}</ul>`;
    }
  }

  return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reservation Update - StayTransit</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f6f7fb; margin: 0; padding: 0; color: #222; }
                .header-bar { background: #fc4f4b; color: #fff; padding: 28px 0 18px 0; text-align: center; }
                .logo { display: inline-block; font-weight: bold; font-size: 28px; color: #fff; letter-spacing: 1px; margin-bottom: 6px; }
                .confirmation { font-size: 22px; font-weight: 700; margin-top: 8px; color: #fff; }
                .greeting { font-size: 15px; color: #fff; margin-block: 4px; }
                .main-content { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 32px 32px 0 32px; position: relative; top: -24px; }
                .section-title { font-size: 18px; font-weight: 700; margin: 24px 0 12px 0; color: #222; }
                .booking-id { font-size: 13px; color: #888; margin-bottom: 8px; }
                .property-name { font-size: 16px; font-weight: 600; color: #fc4f4b; margin-bottom: 2px; }
                .property-address { font-size: 15px; color: #222; margin-bottom: 4px; word-break: break-word; }
                .directions-link { color: #fc4f4b; font-size: 14px; text-decoration: underline; margin-bottom: 10px; display: inline-block; }
                .help-links { font-size: 13px; color: #555; margin-bottom: 10px; }
                .help-links a { color: #fc4f4b; text-decoration: underline; margin-left: 8px; }
                .details-list { margin: 0 0 12px 0; padding: 0; list-style: none; }
                .details-list li { margin-bottom: 6px; font-size: 15px; }
                .details-action { color: #fc4f4b; text-decoration: underline; font-size: 14px; margin-left: 8px; cursor: pointer; }
                .support-section { background: #f6f7fb; border-radius: 8px; padding: 16px; margin: 24px 0; display: flex; align-items: center; justify-content: space-between; }
                .support-title { font-size: 15px; font-weight: 600; }
                .support-btn { background: #fc4f4b; color: #fff; border: none; border-radius: 6px; padding: 8px 18px; font-size: 14px; font-weight: 600; cursor: pointer; }
                .payment-table { width: 100%; border-collapse: collapse; margin: 18px 0; }
                .payment-table td { padding: 10px 0; font-size: 15px; border-bottom: 1px solid #f0f0f0; }
                .payment-table .label { color: #555; }
                .payment-table .total { font-weight: bold; color: #222; font-size: 17px; border-top: 2px solid #fc4f4b; }
                .pay-btn { background: #fc4f4b; color: #fff; border: none; border-radius: 6px; padding: 10px 28px; font-size: 16px; font-weight: 700; cursor: pointer; margin-top: 10px; }
                .cancel-section { background: #f6f7fb; border-radius: 8px; padding: 18px 20px; margin: 24px 0; }
                .cancel-title { font-size: 15px; font-weight: 700; color: #FF715D; margin-bottom: 6px; }
                .cancel-policy { font-size: 14px; color: #555; margin-bottom: 8px; }
                .cancel-btn { background: #fff; border: 1.5px solid #fc4f4b; color: #fc4f4b; padding: 10px 24px; border-radius: 8px; font-size: 15px; font-weight: 700; cursor: pointer; margin-top: 10px; display: inline-block; }
                .rules-section { margin: 24px 0; }
                .rules-title { font-size: 15px; font-weight: 700; color: #222; margin-bottom: 8px; }
                .rules-list { margin: 0 0 0 18px; padding: 0; color: #444; font-size: 14px; }
                .cancellation-policies { margin: 12px 0 0 18px; padding: 0; color: #444; font-size: 14px; line-height: 1.6;}
                .cancellation-policies li { margin-bottom: 8px; list-style-type: disc; }
                @media (max-width: 700px) {
                    .main-content { padding: 12px !important; }
                }
            </style>
        </head>
        <body>
            <div class="header-bar">
                <div class="logo">StayTransit</div>
                <div class="confirmation">Your booking has been updated.</div>
                <div class="greeting">Hello ${reservation.bookerDetails.firstName}, Thank you for choosing StayTransit.</div>
            </div>
            <div class="main-content">
                <div class="section-title">Booking details <span class="booking-id">(ID: ${reservation.reservationCode})</span></div>
                <div class="property-name">${property.name}</div>
                <div class="property-address">${address}</div>
                <a class="directions-link" href="${mapUrl}">Get directions</a>
                <div class="help-links">
                    For special requests or hotel amenities, early check-in, call <a href="tel:${property.poc.phone.countryCode}${property.poc.phone.phoneNumber}">${property.poc.phone.countryCode} ${property.poc.phone.phoneNumber}</a>
                </div>
                <ul class="details-list">
                    <li>Guests: ${reservation.reservations[0]?.noOfAdults || 1} Adult <a href="${updateUrl}"><span class="details-action">Update guest</span></a></li>
                    <li>Rooms: ${reservation.reservations.map((res) => `${(res.packageId as unknown as IPackage)?.name || (res.roomTypeId as unknown as IRoomType)?.name || 'Room'}`).join(', ')}</li>
                </ul>
                <div class="section-title">Payment summary</div>
                <table class="payment-table">
                    <tr>
                        <td class="label">Room Charges</td>
                        <td>Room price for ${reservation.reservations.length} Room x ${reservation.reservations[0]?.noOfAdults || 1} guest</td>
                        <td style="text-align:right;">${formatCurrency(reservation.reservations.reduce((sum, res) => sum + res.price, 0))}</td>
                    </tr>
                    <tr>
                        <td class="label">Coupon Discount</td>
                        <td></td>
                        <td style="text-align:right;">${formatCurrency(totalCouponDiscount)}</td>
                    </tr>
                    <tr>
                        <td class="label">Taxes & Charges</td>
                        <td></td>
                        <td style="text-align:right;">${formatCurrency(reservation.reservations.reduce((sum, res) => sum + res.tax, 0))}</td>
                    </tr>
                    <tr>
                        <td class="label total">Total Paid</td>
                        <td></td>
                        <td class="total" style="text-align:right;">${formatCurrency(reservation.reservations.reduce((sum, res) => sum + res.totalAmount, 0))}</td>
                    </tr>
                </table>
                <div class="cancel-section">
                    <div class="cancel-title">&#9888; Change in plans? No problem</div>
                    <a href="${cancelUrl}"><button class="cancel-btn">Cancel booking</button></a>
                </div>
                <div class="rules-section">
                    <div class="rules-title">Property Rules</div>
                    ${policiesHtml.replace('<ul', '<ul class="rules-list"')}
                </div>
            </div>
        </body>
        </html>
    `;
};
