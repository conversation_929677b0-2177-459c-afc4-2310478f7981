import { FRONTEND_DOMAIN_URL } from '../constants';

export const approvalTemplate = (
  firstName: string,
  status: 'Approved' | 'Rejected' | 'Pending',
) => {
  if (status === 'Pending') {
    return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Your StayTransit Account Has Been Successfully Created</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
                    .container { max-width: 650px; margin: 20px auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #fc4f4b 0%, #c61316 100%); color: white; padding: 30px; text-align: center; }
                    .header h1 { margin: 0; font-size: 28px; font-weight: 600; }
                    .header p { margin: 10px 0 0 0; opacity: 0.9; }
                    .content { padding: 40px; }
                    .section { background: white; padding: 25px; margin: 25px 0; border-radius: 10px; border-left: 5px solid; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
                    .next-steps { border-left-color: #f39c12; }
                    .footer { text-align: center; padding: 30px; background: #c61316; color: white; }
                    .cta-button { display: inline-block; background: #fc4f4b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 0; }
                    .highlight { background: #d5f4e6; padding: 4px 8px; border-radius: 4px; color: #27ae60; font-weight: bold; }
                    @media (max-width: 600px) {
                        .container { margin: 10px; }
                        .content { padding: 20px; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>✅ Account Created</h1>
                        <p>Your StayTransit Account Has Been Successfully Created</p>
                    </div>
                    
                    <div class="content">
                        <p>Dear <strong>${firstName}</strong>,</p>
                        
                        <p>Your StayTransit account has been successfully created. You will soon get a call from our side to complete your onboarding, you will be asked to submit all required property details and documents for review.</p>
                        
                        <div class="section next-steps">
                            <h3>📋 What's Next</h3>
                            <p>We look forward to welcoming your property to our platform.</p>
                        </div>
                        
                        <p style="margin-top: 30px;">
                            <strong>Best regards,</strong><br>
                            StayTransit Team
                        </p>
                    </div>
                    
                    <div class="footer">
                        <p>© 2024 StayTransit. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
  }
  if (status === 'Approved') {
    return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Your StayTransit Account Has Been Approved</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
                    .container { max-width: 650px; margin: 20px auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 30px; text-align: center; }
                    .header h1 { margin: 0; font-size: 28px; font-weight: 600; }
                    .header p { margin: 10px 0 0 0; opacity: 0.9; }
                    .content { padding: 40px; }
                    .section { background: white; padding: 25px; margin: 25px 0; border-radius: 10px; border-left: 5px solid; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
                    .important-notice { border-left-color: #f39c12; background: #fff3cd; }
                    .next-steps { border-left-color: #3498db; }
                    .footer { text-align: center; padding: 30px; background: #2c3e50; color: white; }
                    .cta-button { display: inline-block; background: #27ae60; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 0; }
                    .highlight { background: #d5f4e6; padding: 4px 8px; border-radius: 4px; color: #27ae60; font-weight: bold; }
                    @media (max-width: 600px) {
                        .container { margin: 10px; }
                        .content { padding: 20px; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>✅ Account Approved</h1>
                        <p>Your StayTransit Account Has Been Approved</p>
                    </div>
                    
                    <div class="content">
                        <p>Dear <strong>${firstName}</strong>,</p>
                        
                        <p>We are pleased to inform you that your property has been approved on StayTransit. You now have access to your property dashboard, where you can manage details, set up services, and track your listing performance.</p>
                        
                        <div class="section important-notice">
                            <h3>⚠️ Important Notice</h3>
                            <p><strong>Please note:</strong> Your property will not be visible on the guest website until at least one package or room offering is added and published.</p>
                        </div>
                        
                        <div class="section next-steps">
                            <h3>📋 Next Steps</h3>
                            <p>Kindly log in to your dashboard to complete the package setup and activate your listing.</p>
                            
                            <div style="text-align: center; margin: 20px 0;">
                                <a href="${FRONTEND_DOMAIN_URL}/dashboard" class="cta-button">
                                    Access Dashboard
                                </a>
                            </div>
                        </div>
                        
                        <p style="margin-top: 30px;">
                            <strong>Best regards,</strong><br>
                            StayTransit Team
                        </p>
                    </div>
                    
                    <div class="footer">
                        <p>© 2024 StayTransit. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
  } else {
    return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Your StayTransit Account Submission Was Rejected</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
                    .container { max-width: 650px; margin: 20px auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 30px; text-align: center; }
                    .header h1 { margin: 0; font-size: 28px; font-weight: 600; }
                    .header p { margin: 10px 0 0 0; opacity: 0.9; }
                    .content { padding: 40px; }
                    .section { background: white; padding: 25px; margin: 25px 0; border-radius: 10px; border-left: 5px solid; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
                    .next-steps { border-left-color: #3498db; }
                    .contact-info { border-left-color: #f39c12; }
                    .footer { text-align: center; padding: 30px; background: #2c3e50; color: white; }
                    .cta-button { display: inline-block; background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 0; }
                    .highlight { background: #fadbd8; padding: 4px 8px; border-radius: 4px; color: #e74c3c; font-weight: bold; }
                    @media (max-width: 600px) {
                        .container { margin: 10px; }
                        .content { padding: 20px; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>❌ Account Rejected</h1>
                        <p>Your StayTransit Account Submission Was Rejected</p>
                    </div>
                    
                    <div class="content">
                        <p>Dear <strong>${firstName}</strong>,</p>
                        
                        <p>Thank you for your interest in joining StayTransit. After reviewing your submission, we regret to inform you that your property account has been rejected at this time.</p>
                        
                        <div class="section next-steps">
                            <h3>📋 Next Steps</h3>
                            <p>You may log in to your dashboard to review and update your information, then resubmit for approval.</p>
                            
                            <div style="text-align: center; margin: 20px 0;">
                                <a href="${FRONTEND_DOMAIN_URL}/dashboard" class="cta-button">
                                    Access Dashboard
                                </a>
                            </div>
                        </div>
                        
                        <div class="section contact-info">
                            <h3>📞 Need Help?</h3>
                            <p>For further assistance, please contact <strong><EMAIL></strong>.</p>
                        </div>
                        
                        <p style="margin-top: 30px;">
                            <strong>Best regards,</strong><br>
                            StayTransit Team
                        </p>
                    </div>
                    
                    <div class="footer">
                        <p>© 2024 StayTransit. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
  }
};
