import { FRONTEND_DOMAIN_URL } from '../constants';

export const forgotPasswordTemplate = (firstName: string, token: string) => {
  const resetUrl = `${FRONTEND_DOMAIN_URL}/reset-password?token=${token}`;
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: Arial, sans-serif;
          background-color: #f4f4f4;
          margin: 0;
          padding: 0;
          color: #333;
        }
        .container {
          max-width: 600px;
          margin: 20px auto;
          background-color: #ffffff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
          background-color: #FC4F4B;
          padding: 20px;
          text-align: center;
        }
        .header h1 {
          color: #ffffff;
          margin: 0;
          font-size: 24px;
        }
        .content {
          padding: 30px;
          text-align: center;
        }
        .content p {
          font-size: 16px;
          line-height: 1.6;
          margin: 10px 0;
        }
        .button {
          display: inline-block;
          padding: 12px 24px;
          background-color: #FC4F4B;
          color: #ffffff;
          text-decoration: none;
          border-radius: 5px;
          font-size: 16px;
          margin: 20px 0;
        }
        .button:hover {
          background-color: #e0433f;
        }
        .link {
          font-size: 14px;
          color: #FC4F4B;
          word-break: break-all;
          margin: 20px 0;
        }
        .footer {
          background-color: #f4f4f4;
          padding: 20px;
          text-align: center;
          font-size: 14px;
          color: #666;
        }
        @media only screen and (max-width: 600px) {
          .container {
            margin: 10px;
          }
          .content {
            padding: 20px;
          }
          .header h1 {
            font-size: 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Password Reset Request, ${firstName}!</h1>
        </div>
        <div class="content">
          <p>We received a request to reset your password for your StayTransit account.</p>
          <p>Please click the button below to reset your password:</p>
          <a href="${resetUrl}" class="button">Reset Password</a>
          <p>If the button doesn't work, please copy and paste this link into your browser:</p>
          <p class="link">${resetUrl}</p>
          <p>If you did not request a password reset, please ignore this email.</p>
          <p>Thank you for using StayTransit!</p>
        </div>
        <div class="footer">
          <p>&copy; ${new Date().getFullYear()} StayTransit. All rights reserved.</p>
          <p>Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
      </div>
    </body>
    </html>
  `;
};
