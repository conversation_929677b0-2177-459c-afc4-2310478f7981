import app from './app';
import connectDB from './config/database';
import logger from './utils/logger';
import { PORT } from './constants';

connectDB();

app.listen(PORT, () => {
  logger.info(`Server is running on port ${PORT}`);
});

process.on('uncaughtException', async (error) => {
  const errorMessage = `Uncaught Exception: ${error.message}`;
  logger.error(errorMessage);
  process.exit(1);
});

process.on('unhandledRejection', async (reason) => {
  const errorMessage = `Unhandled Rejection: ${reason}`;
  logger.error(errorMessage);
  process.exit(1);
});
