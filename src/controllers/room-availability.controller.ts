import { Request, Response } from 'express';
import logger from '../utils/logger';
import Joi from 'joi';
import RoomAvailabilityModel, {
  IRoomAvailability,
} from '../models/room-availability.model';
import RoomTypeModel from '../models/room-type.model';

export class RoomAvailabilityController {
  private static Instance: RoomAvailabilityController;

  public static getInstance(): RoomAvailabilityController {
    if (!RoomAvailabilityController.Instance) {
      RoomAvailabilityController.Instance = new RoomAvailabilityController();
    }
    return RoomAvailabilityController.Instance;
  }

  private constructor() {}

  private readonly roomAvailabilitySchema = Joi.array()
    .items(
      Joi.object({
        propertyId: Joi.string().required(),
        roomType: Joi.string().required(),
        dateTime: Joi.date().required(),
        availability: Joi.number().required(),
      }),
    )
    .min(1);

  create() {
    return async (req: Request, res: Response) => {
      try {
        const roomAvailabilities = req.body.roomAvailabilities;

        // Validate the entire array of room availabilities
        const { error } =
          this.roomAvailabilitySchema.validate(roomAvailabilities);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const roomTypes = await RoomTypeModel.find({
          _id: {
            $in: roomAvailabilities.map((ra: IRoomAvailability) => ra.roomType),
          },
        });

        // Collect validation errors
        const validationErrors: string[] = [];
        roomAvailabilities.forEach((ra: IRoomAvailability) => {
          const roomType = roomTypes.find(
            (rt) => rt._id.toString() === ra.roomType.toString(),
          );
          if (!roomType) {
            validationErrors.push(`Room type ${ra.roomType} not found`);
          } else if (roomType.noOfRooms < ra.availability) {
            validationErrors.push(
              `Availability cannot be greater than no of rooms for room type ${roomType.name}`,
            );
          }
        });

        // If there are any validation errors, return them all at once
        if (validationErrors.length > 0) {
          res.status(400).json({ error: validationErrors.join(', ') });
          return;
        }

        // Prepare bulk operations with upsert
        const bulkOperations = roomAvailabilities.map(
          (roomAvailability: IRoomAvailability) => ({
            updateOne: {
              filter: {
                roomType: roomAvailability.roomType,
                dateTime: roomAvailability.dateTime,
                propertyId: req.params.propertyId,
              },
              update: { $set: roomAvailability },
              upsert: true,
            },
          }),
        );

        const result = await RoomAvailabilityModel.bulkWrite(bulkOperations);

        logger.info(
          `Bulk room availability created/updated: ${result.upsertedCount} created, ${result.modifiedCount} updated`,
        );
        res.status(200).json({
          success: true,
          message: 'Room availability created/updated successfully',
          result,
        });
      } catch (error) {
        logger.error(
          `Failed to create room availability: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to create room availability' });
      }
    };
  }

  getByDate() {
    return async (req: Request, res: Response) => {
      try {
        const { date } = req.query;
        const { propertyId } = req.params;

        const startDate = new Date(date as string);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(date as string);
        endDate.setHours(23, 59, 59, 999);

        if (!date) {
          res.status(400).json({
            error: 'Please provide date',
          });
          return;
        }

        const query = {
          propertyId: propertyId,
          dateTime: {
            $gte: startDate,
            $lte: endDate,
          },
        };

        const roomAvailabilities =
          await RoomAvailabilityModel.find(query).populate('roomType');

        logger.info(
          `Fetched room availabilities for property ${propertyId} for ${date}`,
        );
        res.status(200).json(roomAvailabilities);
      } catch (error) {
        logger.error(
          `Failed to fetch room availabilities by date range: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to fetch room availabilities' });
      }
    };
  }
}
