import { Request, Response } from 'express';
import Jo<PERSON> from 'joi';
import logger from '../utils/logger';
import User, { IUser } from '../models/user.model';
import { AuthRequest } from '../middlewares/auth.middleware';
import Property from '../models/property.model';

export class UserController {
  private static Instance: UserController;

  public static getInstance(): UserController {
    if (!UserController.Instance) {
      UserController.Instance = new UserController();
    }
    return UserController.Instance;
  }

  private constructor() {}

  private userUpdateSchema = Joi.object({
    firstName: Joi.string().optional(),
    lastName: Joi.string().optional(),
    phone: Joi.object({
      countryCode: Joi.string().required(),
      phoneNumber: Joi.string().required(),
    }).optional(),
  });

  async getAllUsers(_req: Request, res: Response): Promise<void> {
    try {
      const users: IUser[] = await User.find();
      logger.info('Fetched all users');
      res.status(200).json(users);
    } catch (error) {
      logger.error(`Failed to fetch users: ${(error as Error).message}`);
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  }

  async register(req: Request, res: Response): Promise<void> {
    try {
      const { email, password } = req.body;
      const { error } = this.userSchema.validate(req.body);
      if (error) {
        res.status(400).json({ error: error.details[0].message });
        return;
      }
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        logger.warn(`User with email ${email} already exists`);
        res.status(400).json({ error: 'User already exists' });
        return;
      }

      const user: IUser = new User({ name, email, password });
      await user.save();
      logger.info(`User created: ${user.email}`);
      res.status(201).json(user);
    } catch (error) {
      logger.error(`Failed to create user: ${(error as Error).message}`);
      res.status(500).json({ error: 'Failed to create user' });
    }
  }

  updateUser() {
    return async (req: AuthRequest, res: Response) => {
      try {
        const userId = req.params.userId;
        const userIdFromToken = req.user?.userId;

        if (userId !== userIdFromToken) {
          logger.warn(`User with id ${userId} not found`);
          res.status(404).json({ error: 'User ID mismatch' });
          return;
        }
        const { error } = this.userUpdateSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }
        const user = await User.findByIdAndUpdate(userId, req.body, {
          new: true,
        }).select('-__v -password');

        if (!user) {
          logger.warn(`User with id ${userId} not found`);
          res.status(404).json({ error: 'User not found' });
          return;
        }
        const properties = await Property.find({ 'poc.email': user.email });
        res.status(200).json({ ...user.toObject(), properties });
      } catch (error) {
        logger.error(`Failed to update user: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to update user' });
      }
    };
  }

  userSchema = Joi.object({
    name: Joi.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
  });
}
