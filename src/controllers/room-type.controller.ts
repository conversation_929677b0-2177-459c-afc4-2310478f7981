import { Request, Response } from 'express';
import logger from '../utils/logger';
import Joi from 'joi';
import RoomType, { IRoomType } from '../models/room-type.model';
import mongoose from 'mongoose';

export class RoomTypeController {
  private static Instance: RoomTypeController;

  public static getInstance(): RoomTypeController {
    if (!RoomTypeController.Instance) {
      RoomTypeController.Instance = new RoomTypeController();
    }
    return RoomTypeController.Instance;
  }

  private constructor() {}

  private readonly roomTypeSchema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().allow('').optional(),
    noOfRooms: Joi.number().optional().default(1),
    maxOccupancy: Joi.number().required(),
    area: Joi.number().required(),
    bedType: Joi.string().required(),
    imageUrls: Joi.array().items(Joi.string()).required(),
    amenities: Joi.array().items(Joi.string()).optional(),
    bufferTime: Joi.number().required().default(0),
    customFields: Joi.object().optional(),
  });

  getAll() {
    return async (req: Request, res: Response) => {
      try {
        const { propertyId } = req.params;

        // Validate if propertyId is a valid MongoDB ObjectId
        if (!mongoose.Types.ObjectId.isValid(propertyId)) {
          logger.warn(`Invalid propertyId format: ${propertyId}`);
          res.status(400).json({ error: 'Invalid property ID format' });
          return;
        }

        const roomTypes: IRoomType[] = await RoomType.find({ propertyId });
        logger.info('Fetched all room types');
        res.status(200).json(roomTypes);
      } catch (error) {
        logger.error(`Failed to fetch room types: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch room types' });
      }
    };
  }

  get() {
    return async (req: Request, res: Response) => {
      try {
        const roomType = await RoomType.findById(req.params.id);
        if (!roomType) {
          logger.warn(`Room type with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Room type not found' });
          return;
        }
        logger.info(`Fetched room type: ${roomType.name}`);
        res.status(200).json(roomType);
      } catch (error) {
        logger.error(`Failed to fetch room type: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch room type' });
      }
    };
  }

  create() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.roomTypeSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }
        const propertyId = req.params.propertyId;
        const roomType: IRoomType = req.body;
        const existingRoomType = await RoomType.findOne({
          propertyId,
          name: roomType.name,
        });
        if (existingRoomType) {
          logger.warn(`Room type with name ${roomType.name} already exists`);
          res.status(400).json({ error: 'Room type already exists' });
          return;
        }

        const newRoomType = new RoomType({
          ...roomType,
          propertyId,
        });

        await newRoomType.save();
        logger.info(`Room type created: ${newRoomType.name}`);
        res.status(201).json(newRoomType);
      } catch (error) {
        logger.error(`Failed to create room type: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create room type' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.roomTypeSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const roomType = await RoomType.findById(req.params.id);
        if (!roomType) {
          logger.warn(`Room type with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Room type not found' });
          return;
        }

        const existingRoomType = await RoomType.findOne({
          name: req.body.name,
          _id: { $ne: req.params.id },
          propertyId: req.params.propertyId,
        });

        if (existingRoomType) {
          logger.warn(`Room type with name ${req.body.name} already exists`);
          res.status(400).json({ error: 'Room type name already exists' });
          return;
        }

        const updatedRoomType = await RoomType.findByIdAndUpdate(
          req.params.id,
          req.body,
          { new: true },
        );

        logger.info(`Room type updated: ${updatedRoomType?.name}`);
        res.status(200).json(updatedRoomType);
      } catch (error) {
        logger.error(`Failed to update room type: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to update room type' });
      }
    };
  }

  delete() {
    return async (req: Request, res: Response) => {
      try {
        const roomType = await RoomType.findByIdAndDelete(req.params.id);

        if (!roomType) {
          logger.warn(`Room type with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Room type not found' });
          return;
        }

        logger.info(`Room type deleted: ${roomType.name}`);
        res.status(204).send();
      } catch (error) {
        logger.error(`Failed to delete room type: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to delete room type' });
      }
    };
  }

  updateStatus() {
    return async (req: Request, res: Response) => {
      try {
        const roomType = await RoomType.findByIdAndUpdate(
          req.params.id,
          req.body,
          { new: true },
        );
        if (!roomType) {
          logger.warn(`Room type with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Room type not found' });
          return;
        }
        logger.info(`Room type status updated: ${roomType.name}`);
        res.status(200).json(roomType);
      } catch (error) {
        logger.error(
          `Failed to update room type status: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to update room type status' });
      }
    };
  }
}
