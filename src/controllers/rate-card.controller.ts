import { Request, Response } from 'express';
import logger from '../utils/logger';
import Jo<PERSON> from 'joi';
import RateCard, { IRateCard } from '../models/rate-card.model';

export class RateCardController {
  private static Instance: RateCardController;

  public static getInstance(): RateCardController {
    if (!RateCardController.Instance) {
      RateCardController.Instance = new RateCardController();
    }
    return RateCardController.Instance;
  }

  private constructor() {}

  private readonly rateCardSchema = Joi.array()
    .items(
      Joi.object({
        name: Joi.string().required(),
        description: Joi.string().optional().empty(''),
        propertyId: Joi.string().required(),
        dateTime: Joi.date().required(),
        packageId: Joi.string().required(),
        price: Joi.number().required(),
      }),
    )
    .min(1);

  create() {
    return async (req: Request, res: Response) => {
      try {
        const rateCards = req.body.rateCards;

        // Validate the entire array of rate cards
        const { error } = this.rateCardSchema.validate(rateCards);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        // Prepare bulk operations with upsert
        const bulkOperations = rateCards.map((rateCard: IRateCard) => ({
          updateOne: {
            filter: {
              packageId: rateCard.packageId,
              dateTime: rateCard.dateTime,
              propertyId: req.params.propertyId,
            },
            update: { $set: rateCard },
            upsert: true,
          },
        }));

        const result = await RateCard.bulkWrite(bulkOperations);

        logger.info(
          `Bulk rate cards created/updated: ${result.upsertedCount} created, ${result.modifiedCount} updated`,
        );
        res.status(200).json({
          success: true,
          message: 'Rate cards created/updated successfully',
          result,
        });
      } catch (error) {
        logger.error(
          `Failed to create rate cards: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to create rate cards' });
      }
    };
  }

  getByDate() {
    return async (req: Request, res: Response) => {
      try {
        const { date } = req.query;
        const { propertyId } = req.params;

        if (!date) {
          res.status(400).json({
            error: 'Please provide both start and end dates',
          });
          return;
        }

        const startDate = new Date(date as string);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(date as string);
        endDate.setHours(23, 59, 59, 999);

        const query = {
          propertyId: propertyId,
          dateTime: {
            $gte: startDate,
            $lte: endDate,
          },
        };

        const rateCards = await RateCard.find(query).populate('packageId');

        logger.info(
          `Fetched rate cards for property ${propertyId} for ${date}`,
        );
        res.status(200).json(rateCards);
      } catch (error) {
        logger.error(
          `Failed to fetch rate cards by date range: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to fetch rate cards' });
      }
    };
  }
}
