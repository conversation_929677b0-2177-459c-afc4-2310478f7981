import { Request, Response } from 'express';
import logger from '../utils/logger';
import Application, { IApplication } from '../models/application.model';

export class ApplicationController {
  private static instance: ApplicationController;

  public static getInstance(): ApplicationController {
    if (!ApplicationController.instance) {
      ApplicationController.instance = new ApplicationController();
    }
    return ApplicationController.instance;
  }

  private constructor() {}

  getAll() {
    return async (_req: Request, res: Response) => {
      try {
        const applications: IApplication[] = await Application.find();
        logger.info('Fetched all applications');
        res.status(200).json(applications);
      } catch (error) {
        logger.error(
          `Failed to fetch applications: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to fetch applications' });
      }
    };
  }
}
