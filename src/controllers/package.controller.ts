import { Request, Response } from 'express';
import logger from '../utils/logger';
import Joi from 'joi';
import Package, { IPackage } from '../models/package.model';

export class PackageController {
  private static Instance: PackageController;

  public static getInstance(): PackageController {
    if (!PackageController.Instance) {
      PackageController.Instance = new PackageController();
    }
    return PackageController.Instance;
  }

  private constructor() {}

  private readonly createPackageSchema = Joi.object({
    roomTypeId: Joi.string().required(),
    name: Joi.string().required(),
    description: Joi.string().optional().default('').allow(''),
    duration: Joi.number().required(),
    noOfAdults: Joi.number().required().default(1),
    noOfChildren: Joi.number().required().default(0),
    price: Joi.number().required(),
    taxes: Joi.array().required(),
    amenities: Joi.array().required(),
    customFields: Joi.object().optional(),
  });

  private readonly updatePackageSchema = Joi.object({
    roomTypeId: Joi.string().required(),
    name: Joi.string().optional(),
    description: Joi.string().optional().default('').allow(''),
    duration: Joi.number().optional(),
    noOfAdults: Joi.number().optional().default(1),
    noOfChildren: Joi.number().optional().default(0),
    price: Joi.number().optional(),
    taxes: Joi.array().optional(),
    amenities: Joi.array().optional(),
    customFields: Joi.object().optional(),
    active: Joi.boolean().optional(),
  });

  getAll() {
    return async (req: Request, res: Response) => {
      try {
        const packages: IPackage[] = await Package.find(
          {
            propertyId: req.params.propertyId,
          },
          {},
          { populate: ['roomTypeId', 'amenities', 'taxes'] },
        );
        logger.info('Fetched all packages');
        res.status(200).json(packages);
      } catch (error) {
        logger.error(`Failed to fetch packages: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch packages' });
      }
    };
  }

  get() {
    return async (req: Request, res: Response) => {
      try {
        const pkg = await Package.findById(req.params.id);
        if (!pkg) {
          logger.warn(`Package with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Package not found' });
          return;
        }
        logger.info(`Fetched package: ${pkg.name}`);
        res.status(200).json(pkg);
      } catch (error) {
        logger.error(`Failed to fetch package: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch package' });
      }
    };
  }

  create() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.createPackageSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }
        const propertyId = req.params.propertyId;
        const pkg: IPackage = req.body;
        const existingPackage = await Package.findOne({
          propertyId,
          name: pkg.name,
        });
        if (existingPackage) {
          logger.warn(`Package with name ${pkg.name} already exists`);
          res.status(400).json({ error: 'Package already exists' });
          return;
        }

        const newPackage = new Package({
          ...pkg,
          propertyId,
        });

        await newPackage.save();
        logger.info(`Package created: ${newPackage.name}`);
        res.status(201).json(newPackage);
      } catch (error) {
        logger.error(`Failed to create package: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create package' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.updatePackageSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const pkg = await Package.findById(req.params.id);
        if (!pkg) {
          logger.warn(`Package with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Package not found' });
          return;
        }

        const updatedPackage = await Package.findByIdAndUpdate(
          req.params.id,
          req.body,
          { new: true },
        );

        logger.info(`Package updated: ${updatedPackage?.name}`);
        res.status(200).json(updatedPackage);
      } catch (error) {
        logger.error(`Failed to update package: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to update package' });
      }
    };
  }

  delete() {
    return async (req: Request, res: Response) => {
      try {
        const pkg = await Package.findByIdAndDelete(req.params.id);

        if (!pkg) {
          logger.warn(`Package with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Package not found' });
          return;
        }

        logger.info(`Package deleted: ${pkg.name}`);
        res.status(204).send();
      } catch (error) {
        logger.error(`Failed to delete package: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to delete package' });
      }
    };
  }
}
