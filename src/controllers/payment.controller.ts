import { Request, Response } from 'express';
import logger from '../utils/logger';
import PaymentModel, {
  IPayment,
  PaymentStatusEnum,
} from '../models/payment.model';
import {
  createCheckoutSession,
  createStripeRefund,
  getSessionDetails,
} from '../services/stripe.service';
import Reservation, {
  IReservation,
  ReservationStatusEnum,
} from '../models/reservation.model';
import Joi from 'joi';
import { EmailService } from '../services/email.service';
import Property, { IProperty } from '../models/property.model';
import { validatePayment, validateRefundInputs } from '../utils/cancellation';
import { IPackage } from '../models/package.model';

export class PaymentController {
  private static instance: PaymentController;
  private emailService: EmailService;

  private constructor() {
    this.emailService = new EmailService();
  }

  public static getInstance(): PaymentController {
    if (!PaymentController.instance) {
      PaymentController.instance = new PaymentController();
    }
    return PaymentController.instance;
  }

  private readonly paymentSchema = Joi.object({
    amount: Joi.number(),
    currency: Joi.string().required(),
    reservationId: Joi.string().required(),
    propertyId: Joi.string().required(),
    status: Joi.string().required(),
    paymentMethod: Joi.string().required(),
    paymentGateway: Joi.string(),
    paymentGatewayResponse: Joi.string(),
  });

  get() {
    return async (req: Request, res: Response) => {
      try {
        const payment = await PaymentModel.findById(req.params.id);
        if (!payment) {
          logger.warn(`Payment with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Payment not found' });
          return;
        }
        logger.info(`Fetched payment: ${payment}`);
        res.status(200).json(payment);
      } catch (error) {
        logger.error(`Failed to fetch payment: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch payment' });
      }
    };
  }

  create() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.paymentSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const groupReservation = await Reservation.findById(
          req.body.reservationId,
        );
        const amount = groupReservation?.reservations.reduce(
          (acc, reservation) => acc + reservation.totalAmount,
          0,
        );

        const payment = new PaymentModel({ amount, ...req.body });
        await payment.save();
        logger.info(`Payment created: ${payment}`);
        res.status(201).json(payment);
      } catch (error) {
        logger.error(`Failed to create payment: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create payment' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.paymentSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const updatedPayment = await PaymentModel.findByIdAndUpdate(
          req.params.id,
          req.body,
          { new: true },
        );

        logger.info(`Payment updated: ${updatedPayment}`);
        res.status(200).json(updatedPayment);
      } catch (error) {
        logger.error(`Failed to update payment: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to update payment' });
      }
    };
  }

  updateStatus() {
    return async (req: Request, res: Response) => {
      try {
        const payment = await PaymentModel.findById(req.params.id);

        if (!payment) {
          res.status(404).json({ error: 'Payment not found' });
          return;
        }

        let reservation;

        if (
          payment.status === PaymentStatusEnum.PAID ||
          payment.status === PaymentStatusEnum.REFUNDED ||
          payment.status === PaymentStatusEnum.FAILED
        ) {
          reservation = await Reservation.findOne({
            groupReservationId: payment.reservationId,
          }).populate([
            {
              path: 'reservations.packageId',
            },
            {
              path: 'reservations.roomTypeId',
            },
            {
              path: 'reservations.taxes',
            },
            {
              path: 'propertyId',
            },
          ]);

          if (!reservation) {
            res.status(404).json({ error: 'Reservation not found' });
            return;
          }

          res.status(200).json({
            reservation,
            message: `Payment already marked as ${payment.status}`,
          });
          return;
        }

        const session = await getSessionDetails(
          payment.paymentGatewayResponse?.sessionId as string,
        );

        switch (session.payment_status) {
          case 'no_payment_required':
            await PaymentModel.findByIdAndUpdate(
              req.params.id,
              { status: PaymentStatusEnum.PAID },
              { new: true },
            );
            reservation = await Reservation.findOneAndUpdate(
              { groupReservationId: payment.reservationId },
              {
                status: ReservationStatusEnum.CONFIRMED,
                paymentStatus: PaymentStatusEnum.PAID,
              },
              { new: true },
            ).populate([
              {
                path: 'reservations.packageId',
              },
              {
                path: 'reservations.roomTypeId',
              },
              {
                path: 'propertyId',
              },
            ]);

            if (reservation) {
              const property = await Property.findById(payment?.propertyId);
              if (property) {
                logger.info(
                  `Reservation confirmed without payment: ${reservation}`,
                );
                await this.emailService.sendReservationConfirmationEmail(
                  reservation.bookerDetails.email,
                  reservation.bookerDetails.firstName,
                  reservation,
                  property,
                );
              }
            }
            res.status(200).json({
              reservation,
              message: `Payment marked as ${PaymentStatusEnum.PAID}`,
            });
            break;

          case 'paid':
            await Reservation.findByIdAndUpdate(
              payment.reservationId,
              { status: ReservationStatusEnum.CONFIRMED },
              { new: true },
            );

            await PaymentModel.findByIdAndUpdate(
              req.params.id,
              { status: PaymentStatusEnum.PAID },
              { new: true },
            );

            reservation = await Reservation.findOneAndUpdate(
              { groupReservationId: payment.reservationId },
              {
                status: ReservationStatusEnum.CONFIRMED,
                paymentStatus: PaymentStatusEnum.PAID,
              },
              { new: true },
            ).populate([
              {
                path: 'reservations.packageId',
              },
              {
                path: 'reservations.roomTypeId',
              },
              {
                path: 'reservations.taxes',
              },
              {
                path: 'propertyId',
              },
            ]);

            if (reservation) {
              const property = await Property.findById(payment?.propertyId);
              if (property) {
                logger.info(`Reservation updated: ${reservation._id}`);
                await this.emailService.sendReservationConfirmationEmail(
                  reservation.bookerDetails.email,
                  reservation.bookerDetails.firstName,
                  reservation,
                  property,
                );
              }
            }

            res.status(200).json({ reservation, message: 'Payment marked' });
            break;

          case 'unpaid':
            await PaymentModel.findByIdAndUpdate(
              req.params.id,
              { status: PaymentStatusEnum.FAILED },
              { new: true },
            );
            reservation = await Reservation.findOneAndUpdate(
              { groupReservationId: payment.reservationId },
              {
                status: ReservationStatusEnum.CANCELLED,
                paymentStatus: PaymentStatusEnum.FAILED,
              },
              { new: true },
            );
            res.status(200).json({ reservation, message: 'Payment failed' });
            break;

          default:
            res.status(400).json({ error: 'Invalid Stripe session status' });
            return;
        }
      } catch (error) {
        logger.error(
          `Failed to update payment status: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to update payment status' });
      }
    };
  }

  delete() {
    return async (req: Request, res: Response) => {
      try {
        const payment = await PaymentModel.findByIdAndDelete(req.params.id);

        if (!payment) {
          logger.warn(`Payment with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Payment not found' });
          return;
        }

        logger.info(`Payment deleted: ${payment}`);
        res.status(204).send();
      } catch (error) {
        logger.error(`Failed to delete payment: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to delete payment' });
      }
    };
  }

  checkout() {
    return async (req: Request, res: Response) => {
      const property = await Property.findById(req.body.propertyId);

      if (!property) {
        logger.warn(`Property with id ${req.body.propertyId} not found`);
        res.status(404).json({ error: 'Property not found' });
        return;
      }

      const reservation = await Reservation.findOne({
        groupReservationId: req.body.reservationId,
      }).populate<{
        reservations: {
          totalAmount: number;
          packageId: IPackage;
        }[];
      }>('reservations.packageId');

      if (!reservation) {
        logger.warn(`Reservation with id ${req.body.reservationId} not found`);
        res.status(404).json({ error: 'Reservation not found' });
        return;
      }

      const amount =
        reservation?.reservations.reduce(
          (acc, reservationData) => acc + reservationData.totalAmount,
          0,
        ) || 0;

      try {
        const payment = await PaymentModel.create({
          reservationId: req.body.reservationId,
          propertyId: req.body.propertyId,
          amount: amount,
          currency: req.body.currency,
          paymentMethod: req.body.paymentMethod,
        });

        if (req.params.paymentProvider === 'stripe') {
          const response = await createCheckoutSession(
            req.body.currency,
            payment,
            reservation as unknown as IReservation,
          );
          await PaymentModel.findByIdAndUpdate(payment._id, {
            paymentGateway: 'stripe',
            paymentGatewayResponse: response,
          });
          res.status(200).json(response);
        }
      } catch (error) {
        logger.error(`Failed to checkout: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to checkout' });
      }
    };
  }

  refund() {
    return async (req: Request, res: Response) => {
      try {
        const { reservationId: groupReservationId, paymentProvider } =
          req.params;
        const { cancellationTime } = req.query;

        const validationResult = await validateRefundInputs(
          groupReservationId,
          cancellationTime as string,
          res,
          true,
        );
        if (!validationResult) return;

        const { refundPolicy, refundableAmount } = validationResult;

        const reservation = await Reservation.findOneAndUpdate(
          { groupReservationId },
          {
            $set: {
              paymentStatus: 'refunded',
              status: 'cancelled',
              'reservations.$[].status': 'cancelled',
            },
          },
          { new: true },
        );

        if (!reservation) {
          logger.warn(`Reservation with id ${groupReservationId} not found`);
          res.status(404).json({ error: 'Reservation not found' });
          return;
        }

        const property = await Property.findById(reservation.propertyId);

        if (!property) {
          logger.warn(`Property with id ${reservation.propertyId} not found`);
          res.status(404).json({ error: 'Property not found' });
          return;
        }

        if (refundPolicy.refund_percent <= 0) {
          await this.emailService.sendCancellationEmail(
            reservation.bookerDetails.email,
            reservation,
            property,
          );
          res.status(200).json({
            refundableAmount: 0,
            refundablePercentage: 0,
            description: refundPolicy.description,
          });
          return;
        }

        const payment = await validatePayment(groupReservationId, res);
        if (!payment) return;

        logger.info(`Refunding payment: ${payment._id}`);
        if (paymentProvider === 'stripe') {
          await this.processStripeRefund(
            payment,
            refundableAmount,
            groupReservationId,
            res,
            property as IProperty,
          );
        }
      } catch (error) {
        logger.error(`Failed to refund payment: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to refund payment' });
      }
    };
  }

  async processStripeRefund(
    payment: IPayment,
    refundableAmount: number,
    groupReservationId: string,
    res: Response,
    property: IProperty,
  ): Promise<boolean> {
    const sessionId = payment.paymentGatewayResponse?.sessionId;
    if (!sessionId) {
      logger.warn(
        `No session id found for payment with Group Reservation id ${groupReservationId}`,
      );
      res.status(400).json({ error: 'No Payment Info Found' });
      return false;
    }

    const refundData = await createStripeRefund(
      sessionId as string,
      refundableAmount,
    );
    await PaymentModel.findByIdAndUpdate(payment._id, {
      status: 'refunded',
      refundResponse: { ...refundData, amount: refundableAmount },
    });

    const reservation = await Reservation.findOneAndUpdate(
      { groupReservationId },
      {
        $set: {
          paymentStatus: 'refunded',
          status: 'cancelled',
          'reservations.$[].status': 'cancelled',
          refundAmount: refundableAmount,
          cancellationReason: 'Refunded',
        },
      },
      { new: true },
    );

    if (!reservation) {
      logger.warn(`Reservation with id ${groupReservationId} not found`);
      res.status(404).json({ error: 'Reservation not found' });
      return false;
    }

    await this.emailService.sendCancellationEmail(
      reservation.bookerDetails.email,
      reservation,
      property,
    );

    res.status(200).json({ reservation });
    return true;
  }
}
