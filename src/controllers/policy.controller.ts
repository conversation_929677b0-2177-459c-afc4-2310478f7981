import { Request, Response } from 'express';
import * as Jo<PERSON> from 'joi';
import logger from '../utils/logger';
import Policy, { IPolicy } from '../models/policy.model';
import mongoose from 'mongoose';

// Validation schema for policy creation/update
const policyValidationSchema = Joi.object({
  name: Joi.string().required().trim().min(1).max(100),
  description: Joi.string().optional().empty('').trim().max(500),
  category: Joi.string().empty('').optional(),
  serviceType: Joi.string().required(),
  order: Joi.number().optional().default(0),
  input_type: Joi.string()
    .valid('text', 'radio', 'checkbox', 'select', 'custom_rule')
    .required(),
  options: Joi.array()
    .items(
      Joi.object({
        value: Joi.string().trim().required(),
        description: Joi.string().trim().optional().empty(''),
      }),
    )
    .optional(),
  is_required: Joi.boolean().required(),
  validation_rules: Joi.object({
    min: Joi.number().optional(),
    max: Joi.number().optional(),
    pattern: Joi.string().allow('').optional(),
    required: Joi.boolean().optional(),
  }).optional(),
  conditional_logic: Joi.object({
    field: Joi.string().required(),
    value: Joi.any().required(),
    operator: Joi.string()
      .valid('equals', 'not_equals', 'contains', 'greater_than', 'less_than')
      .required(),
  }).optional(),
  config: Joi.object().optional(),
  propertyId: Joi.string().optional(),
});

export class PolicyController {
  private static Instance: PolicyController;

  public static getInstance(): PolicyController {
    if (!PolicyController.Instance) {
      PolicyController.Instance = new PolicyController();
    }
    return PolicyController.Instance;
  }

  private constructor() {}

  create() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = policyValidationSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const policy = await Policy.create(req.body);

        logger.info(`Policy created: ${policy.name}`);
        res.status(201).json({
          message: 'Policy template created successfully',
          policy,
        });
      } catch (error) {
        logger.error(`Failed to create policy: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create policy template' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = policyValidationSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const policy = await Policy.findByIdAndUpdate(req.params.id, req.body);

        if (!policy) {
          res.status(404).json({ error: 'Policy template not found' });
          return;
        }

        logger.info(`Policy updated`);
        res.status(201).json({
          message: 'Policy template updated successfully',
          policy,
        });
      } catch (error) {
        logger.error(`Failed to create policy: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create policy template' });
      }
    };
  }

  getById() {
    return async (req: Request, res: Response) => {
      try {
        const policy = await Policy.findById(req.params.id);

        if (!policy) {
          res.status(404).json({ error: 'Policy template not found' });
          return;
        }

        logger.info(`Policy fetched`);
        res.status(201).json({
          message: 'Policy template fetched successfully',
          policy,
        });
      } catch (error) {
        logger.error(`Failed to create policy: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create policy template' });
      }
    };
  }

  // Get all policies (with optional filtering)
  getAll() {
    return async (req: Request, res: Response) => {
      try {
        const {
          propertyId,
          input_type,
          is_required,
          page = 1,
          limit = 10,
        } = req.query;

        const filter: mongoose.FilterQuery<IPolicy> = { deleted: false };

        if (propertyId) filter.propertyId = propertyId;
        if (input_type) filter.input_type = input_type;
        if (is_required !== undefined)
          filter.is_required = is_required === 'true';

        const skip = (Number(page) - 1) * Number(limit);

        const policies = await Policy.find(filter)
          .populate('category')
          .sort({ category: 1, createdAt: 1 })
          .skip(skip);

        const total = await Policy.countDocuments(filter);

        logger.info(`Fetched ${policies.length} policies`);
        res.status(200).json({
          policies,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit)),
          },
        });
      } catch (error) {
        logger.error(`Failed to fetch policies: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch policies' });
      }
    };
  }

  delete() {
    return async (req: Request, res: Response) => {
      try {
        const policy = await Policy.findByIdAndDelete(req.params.id);

        if (!policy) {
          res.status(404).json({ error: 'Policy template not found' });
          return;
        }

        logger.info(`Policy deleted: ${policy.name}`);
        res.status(200).json({
          message: 'Policy template deleted successfully',
        });
      } catch (error) {
        logger.error(`Failed to delete policy: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to delete policy template' });
      }
    };
  }

  // Validate policy logic based on input_type
  // private validatePolicyLogic(data: any): string | null {
  //   const { input_type, options, config } = data;
  //
  //   // Validate options for radio/checkbox/select
  //   if (['radio', 'checkbox', 'select'].includes(input_type)) {
  //     if (!options || !Array.isArray(options) || options.length === 0) {
  //       return `Options array is required for input_type: ${input_type}`;
  //     }
  //
  //     // Check for duplicate options
  //     const uniqueOptions = new Set(options);
  //     if (uniqueOptions.size !== options.length) {
  //       return 'Options array contains duplicate values';
  //     }
  //   }
  //
  //   // Validate config for custom_rule
  //   if (input_type === 'custom_rule') {
  //     if (!config || !config.rules || !Array.isArray(config.rules) || config.rules.length === 0) {
  //       return 'Config.rules array is required for custom_rule input_type';
  //     }
  //
  //     // Validate each rule has required fields
  //     for (const rule of config.rules) {
  //       if (rule.refund_percent === undefined || rule.before_hours === undefined) {
  //         return 'Each rule must have refund_percent and before_hours fields';
  //       }
  //
  //       if (rule.refund_percent < 0 || rule.refund_percent > 100) {
  //         return 'Refund percentage must be between 0 and 100';
  //       }
  //
  //       if (rule.before_hours < 0) {
  //         return 'Before hours must be a positive number';
  //       }
  //     }
  //   }
  //
  //   return null;
  // }
}
