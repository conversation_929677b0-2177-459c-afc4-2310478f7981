import { Request, Response } from 'express';
import logger from '../utils/logger';
import Property, { IProperty } from '../models/property.model';
import Package, { IPackage } from '../models/package.model';
import Location, { ILocation } from '../models/location.model';
import { EmailService } from '../services/email.service';
import Joi from 'joi';
import mongoose from 'mongoose';
import RateCardSchema, { IRateCard } from '../models/rate-card.model';
import { IRoomType } from '../models/room-type.model';
import { checkRoomAvailability } from '../utils/availability';

export class PropertyController {
  private static instance: PropertyController;
  private emailService: EmailService;

  private constructor() {
    this.emailService = new EmailService();
  }

  public static getInstance(): PropertyController {
    if (!PropertyController.instance) {
      PropertyController.instance = new PropertyController();
    }
    return PropertyController.instance;
  }

  private readonly propertySchema = Joi.object({
    name: Joi.string().required().min(2).max(50),
    description: Joi.string(),
    website: Joi.string(),
    logoUrl: Joi.string(),
    email: Joi.string().email(),
    fax: Joi.string(),
    premisesType: Joi.string().required().valid('Airside', 'Landside'),
    phone: Joi.object({
      countryCode: Joi.string().required(),
      phoneNumber: Joi.string().required(),
    }).required(),
    address: Joi.object({
      address1: Joi.string().required(),
      address2: Joi.string(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      country: Joi.string().required(),
      zipcode: Joi.string().empty('').optional(),
      latitude: Joi.number(),
      longitude: Joi.number(),
      placeId: Joi.string().empty('').optional(),
      locationId: Joi.string().optional(),
    }).required(),
    poc: Joi.object({
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      email: Joi.string().email().required(),
      phone: Joi.object({
        countryCode: Joi.string().required(),
        phoneNumber: Joi.string().required(),
      }).required(),
    }).required(),
    customFields: Joi.object().pattern(Joi.string(), Joi.string()).optional(),
  });

  getAll() {
    return async (req: Request, res: Response) => {
      try {
        const propertyFilterQuery: mongoose.FilterQuery<IProperty> = {};

        const city = req.query.city as string | undefined;
        const locationId = req.query.locationId as string | undefined;
        const active = req.query.active as string | undefined;

        if (locationId) {
          const location = await Location.findById(locationId);
          if (location) {
            const allCityLocations: ILocation[] = await Location.find({
              city: location.city,
            });

            propertyFilterQuery['address.locationId'] = {
              $in: allCityLocations.map((l) => l._id),
            };
          }
        } else if (city) {
          propertyFilterQuery['address.city'] = city;
        }

        if (active === 'true') {
          propertyFilterQuery['active'] = true;
        }

        const properties = await Property.find(propertyFilterQuery)
          .lean()
          .sort({ updatedAt: -1 })
          .populate([
            {
              path: 'address.locationId',
            },
            {
              path: 'customFields.serviceType',
              model: 'DomainValue',
              select: 'code name',
            },
            {
              path: 'customFields.businessDetails.commission.frequency',
              model: 'DomainValue',
              select: 'code name',
            },
          ]);

        if (req.query.includesPackages === 'true') {
          const {
            duration,
            noOfAdults,
            noOfChildren,
            startDateTime,
            endDateTime,
          } = req.query;

          const packageFilter: mongoose.FilterQuery<IPackage> = {
            propertyId: { $in: properties.map((p) => p._id) },
          };

          if (duration) {
            packageFilter.duration = { $gte: parseInt(duration as string, 10) };
          }

          if (noOfAdults) {
            packageFilter.noOfAdults = parseInt(noOfAdults as string, 10);
          }

          if (noOfChildren) {
            packageFilter.noOfChildren = parseInt(noOfChildren as string, 10);
          }

          if (startDateTime && endDateTime) {
            const start = new Date(startDateTime as string);
            const end = new Date(endDateTime as string);
            packageFilter.duration = {
              $gte: Math.ceil(
                (end.getTime() - start.getTime()) / (1000 * 60 * 60),
              ),
            };
          }

          let packages = await Package.find(packageFilter)
            .populate('roomTypeId')
            .populate('taxes')
            .lean();

          if (startDateTime && endDateTime) {
            const startDate = new Date(startDateTime as string);
            const endDate = new Date(endDateTime as string);

            if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
              const packageIds = packages.map(
                (pkg) => new mongoose.Types.ObjectId(pkg._id),
              );
              const rateCards: IRateCard[] = await RateCardSchema.find({
                packageId: { $in: packageIds },
                date: {
                  $gte: startDate.toISOString().split('T')[0],
                  $lte: endDate.toISOString().split('T')[0],
                },
              }).lean();

              const rateCardMap = rateCards.reduce(
                (acc, rateCard) => {
                  const packageId = rateCard.packageId.toString();
                  if (!acc[packageId]) {
                    acc[packageId] = rateCard.price;
                  }
                  return acc;
                },
                {} as Record<string, number>,
              );

              const uniqueRoomTypes = Array.from(
                new Set(
                  packages.map((pkg) => pkg.roomTypeId as unknown as IRoomType),
                ),
              );

              // Modified to handle each propertyId individually
              const availabilityResults = await Promise.all(
                properties.map(async (property) => {
                  return await checkRoomAvailability({
                    propertyId: property._id.toString(),
                    roomTypes: uniqueRoomTypes,
                    startDateTime: startDateTime as string,
                    endDateTime: endDateTime as string,
                  });
                }),
              );

              // Flatten and create availability map
              const availabilityMap = new Map(
                availabilityResults
                  .flat()
                  .map((result) => [result.roomTypeId, result.availableRooms]),
              );

              packages = packages
                .map((pkg) => {
                  const rtId = (
                    pkg.roomTypeId as unknown as IRoomType
                  )._id.toString();
                  const availableRooms = availabilityMap.get(rtId) || 0;

                  return {
                    ...pkg,
                    rateCardPrice:
                      rateCardMap[pkg._id.toString()] !== undefined
                        ? rateCardMap[pkg._id.toString()]
                        : null,
                    available: availableRooms,
                  };
                })
                .filter((pkg) => pkg.available > 0);
            } else {
              logger.warn('Invalid startDateTime or endDateTime provided');
              packages = packages.map((pkg) => ({
                ...pkg,
                rateCardPrice: null,
                available: (pkg.roomTypeId as unknown as IRoomType).noOfRooms,
              }));
            }
          }

          properties.forEach((property) => {
            property.packages = packages.filter(
              (pkg) => property._id.toString() === pkg.propertyId.toString(),
            );
          });
        }

        res.status(200).json(properties);
      } catch (error) {
        logger.error(`Failed to fetch properties: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch properties' });
      }
    };
  }

  get() {
    return async (req: Request, res: Response) => {
      try {
        const property = await Property.findById(req.params.id).populate([
          {
            path: 'address.locationId',
          },
        ]);
        if (!property) {
          logger.warn(`Property with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Property not found' });
          return;
        }
        logger.info(`Fetched property: ${property.name}`);
        res.status(200).json(property);
      } catch (error) {
        logger.error(`Failed to fetch property: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch property' });
      }
    };
  }

  create() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.propertySchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const property: IProperty = req.body;
        const existingProperty = await Property.findOne({
          name: property.name,
        });
        if (existingProperty) {
          logger.warn(`Property with name ${property.name} already exists`);
          res.status(400).json({ error: 'Property already exists' });
          return;
        }

        const newProperty = new Property({
          ...property, // Include all validated fields
          name: property.name,
          address: property.address,
          phone: property.phone,
          poc: property.poc,
        });

        await newProperty.save();

        await this.emailService.sendApprovalStatusEmail(
          property.poc.email,
          property.poc.firstName,
          property,
          'Pending',
        );
        logger.info(
          `Property created: ${newProperty.name} in ${newProperty.address.city}`,
        );
        res.status(201).json(newProperty);
      } catch (error) {
        logger.error(`Failed to create property: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create property' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.propertySchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const property = await Property.findById(req.params.id);
        if (!property) {
          logger.warn(`Property with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Property not found' });
          return;
        }

        const existingProperty = await Property.findOne({
          name: req.body.name,
          _id: { $ne: req.params.id },
        });

        if (existingProperty) {
          logger.warn(`Property with name ${req.body.name} already exists`);
          res.status(400).json({ error: 'Property name already exists' });
          return;
        }

        const updatedProperty = await Property.findByIdAndUpdate(
          req.params.id,
          req.body,
          { new: true },
        );

        logger.info(`Property updated: ${updatedProperty?.name}`);
        res.status(200).json(updatedProperty);
      } catch (error) {
        logger.error(`Failed to update property: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to update property' });
      }
    };
  }

  delete() {
    return async (req: Request, res: Response) => {
      try {
        const property = await Property.findByIdAndDelete(req.params.id);

        if (!property) {
          logger.warn(`Property with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Property not found' });
          return;
        }

        logger.info(`Property deleted: ${property.name}`);
        res.status(204).send();
      } catch (error) {
        logger.error(`Failed to delete property: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to delete property' });
      }
    };
  }

  patchCustomFields() {
    return async (req: Request, res: Response) => {
      try {
        const property = await Property.findByIdAndUpdate(
          req.params.id,
          { customFields: req.body.customFields },
          { new: true },
        );
        if (!property) {
          res.status(404).json({ error: 'Property not found' });
          return;
        }
        res.status(200).json(property);
      } catch (error) {
        logger.error(
          `Failed to patch customFields: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to update customFields' });
      }
    };
  }

  patchStatus() {
    return async (req: Request, res: Response) => {
      try {
        const { status, active } = req.body;

        // Get the current property to compare status changes
        const currentProperty = await Property.findById(req.params.id);
        if (!currentProperty) {
          res.status(404).json({ error: 'Property not found' });
          return;
        }

        const property = await Property.findByIdAndUpdate(
          req.params.id,
          { status, active },
          { new: true },
        );

        if (!property) {
          res.status(404).json({ error: 'Property not found' });
          return;
        }

        // Send email notifications for status changes
        try {
          // Check if approval status changed
          if (
            status &&
            status !== currentProperty.status &&
            (status === 'Approved' || status === 'Rejected')
          ) {
            await this.emailService.sendApprovalStatusEmail(
              property.poc.email,
              property.poc.firstName,
              property,
              status,
            );
          }

          // Check if active status changed
          if (active !== undefined && active !== currentProperty.active) {
            await this.emailService.sendActiveStatusEmail(
              property.poc.email,
              property.poc.firstName,
              property,
              active,
            );
          }
        } catch (emailError) {
          logger.error(
            `Failed to send status change email: ${(emailError as Error).message}`,
          );
          // Don't fail the request if email fails
        }

        res.status(200).json(property);
      } catch (error) {
        logger.error(`Failed to patch status: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to update status' });
      }
    };
  }
}
