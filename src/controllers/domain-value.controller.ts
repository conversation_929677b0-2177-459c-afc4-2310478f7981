import { Request, Response } from 'express';
import logger from '../utils/logger';
import DomainValue, { IDomainValue } from '../models/domain-value.model';
import Joi from 'joi';
import mongoose from 'mongoose';

export class DomainValueController {
  private static instance: DomainValueController;

  public static getInstance(): DomainValueController {
    if (!DomainValueController.instance) {
      DomainValueController.instance = new DomainValueController();
    }
    return DomainValueController.instance;
  }

  private constructor() {}

  private readonly domainValueSchema = Joi.object({
    propertyId: Joi.string().required(),
    category: Joi.string().optional().empty(''),
    level: Joi.string().required(),
    code: Joi.string().required(),
    name: Joi.string().required(),
    description: Joi.string().optional().empty(''),
    displayOrder: Joi.number().optional(),
    categoryId: Joi.string().optional().empty(''),
    parentId: Joi.string().optional().empty(''),
    value: Joi.string().optional().empty(''),
    icon: Joi.string().optional().empty(''),
  });

  getAll() {
    return async (req: Request, res: Response) => {
      try {
        const { propertyId, categoryId, level, parentId } = req.query;
        const query: mongoose.FilterQuery<IDomainValue> = {};

        if (propertyId) {
          query.propertyId = propertyId;
        }

        if (categoryId) {
          query.categoryId = new mongoose.Types.ObjectId(categoryId as string);
        }

        if (level) {
          query.level = level;
        }

        if (parentId) {
          query.parentId = new mongoose.Types.ObjectId(parentId as string);
        }

        const domainValues: IDomainValue[] = await DomainValue.find(query);
        logger.info(
          `Fetched all domain values for propertyId ${propertyId}, categoryId ${categoryId}, level ${level}, parentId ${parentId}`,
        );
        res.status(200).json(domainValues);
      } catch (error) {
        logger.error(
          `Failed to fetch domain values: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to fetch domain values' });
      }
    };
  }

  create() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.domainValueSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const { name, level, parentId, propertyId, categoryId } = req.body;

        const existingDomainValue = await DomainValue.findOne({
          name,
          level,
          parentId,
          propertyId,
          categoryId,
        });
        if (existingDomainValue) {
          res.status(400).json({ error: 'Domain value already exists' });
          return;
        }

        const domainValue: IDomainValue = await DomainValue.create(req.body);
        logger.info('Created domain value');
        res.status(201).json(domainValue);
      } catch (error) {
        logger.error(
          `Failed to create domain value: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to create domain value' });
      }
    };
  }

  getById() {
    return async (req: Request, res: Response) => {
      try {
        const domainValue = await DomainValue.findById(req.params.id);
        if (!domainValue) {
          res.status(404).json({ error: 'Domain value not found' });
          return;
        }
        logger.info('Retrieved domain value by ID');
        res.status(200).json(domainValue);
      } catch (error) {
        logger.error(
          `Failed to retrieve domain value: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to retrieve domain value' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.domainValueSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const domainValue = await DomainValue.findByIdAndUpdate(
          req.params.id,
          req.body,
          { new: true },
        );
        if (!domainValue) {
          res.status(404).json({ error: 'Domain value not found' });
          return;
        }
        logger.info('Updated domain value');
        res.status(200).json(domainValue);
      } catch (error) {
        logger.error(
          `Failed to update domain value: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to update domain value' });
      }
    };
  }

  delete() {
    return async (req: Request, res: Response) => {
      try {
        const domainValue = await DomainValue.findByIdAndDelete(req.params.id);
        if (!domainValue) {
          res.status(404).json({ error: 'Domain value not found' });
          return;
        }
        logger.info('Deleted domain value');
        res.status(200).json(domainValue);
      } catch (error) {
        logger.error(
          `Failed to delete domain value: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to delete domain value' });
      }
    };
  }

  getByLevel() {
    return async (req: Request, res: Response) => {
      try {
        const domainValue = await DomainValue.find({
          level: req.params.level,
        });
        if (!domainValue) {
          res.status(404).json({ error: 'Domain value not found' });
          return;
        }
        logger.info('Retrieved domain value by ID');
        res.status(200).json(domainValue);
      } catch (error) {
        logger.error(
          `Failed to retrieve domain value: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to retrieve domain value' });
      }
    };
  }

  getByCategoryId() {
    return async (req: Request, res: Response) => {
      try {
        const domainValue = await DomainValue.find({
          categoryId: req.params.categoryId,
        });
        logger.info('Retrieved domain value by categoryId');
        res.status(200).json(domainValue);
      } catch (error) {
        logger.error(
          `Failed to retrieve domain value: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to retrieve domain value' });
      }
    };
  }

  getByCategoryIdAndPropertyId() {
    return async (req: Request, res: Response) => {
      try {
        const domainValue = await DomainValue.find({
          categoryId: req.params.categoryId,
          propertyId: req.params.propertyId,
        });
        logger.info('Retrieved domain value by categoryId and propertyId');
        res.status(200).json(domainValue);
      } catch (error) {
        logger.error(
          `Failed to retrieve domain value: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to retrieve domain value' });
      }
    };
  }
}
