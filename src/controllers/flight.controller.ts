import { Request, Response } from 'express';
import logger from '../utils/logger';
import Flight, { IFlight } from '../models/flight.model';

export class FlightController {
  private static instance: FlightController;

  public static getInstance(): FlightController {
    if (!FlightController.instance) {
      FlightController.instance = new FlightController();
    }
    return FlightController.instance;
  }

  private constructor() {}

  getAll() {
    return async (_req: Request, res: Response) => {
      try {
        const flights: IFlight[] = await Flight.find();
        logger.info('Fetched all flights');
        res.status(200).json(flights);
      } catch (error) {
        logger.error(`Failed to fetch flights: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch flights' });
      }
    };
  }
}
