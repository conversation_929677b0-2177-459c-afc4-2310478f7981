import { Request, Response } from 'express';
import logger from '../utils/logger';
import Location, { ILocation } from '../models/location.model';
import axios from 'axios';
import { countryCodes } from '../utils/countryCodes';

export class LocationController {
  private static Instance: LocationController;

  public static getInstance(): LocationController {
    if (!LocationController.Instance)
      LocationController.Instance = new LocationController();
    return LocationController.Instance;
  }

  private constructor() {}

  getAll() {
    return async (req: Request, res: Response) => {
      try {
        const { search } = req.query;
        let locations: ILocation[];

        if (search) {
          const searchRegex = new RegExp(String(search), 'i');
          locations = await Location.find({
            $or: [
              { code: searchRegex },
              { name: searchRegex },
              { city: searchRegex },
            ],
            deleted: false,
          }).sort({
            updatedAt: -1,
          });
        } else {
          locations = await Location.find({ deleted: false }).sort({
            updatedAt: -1,
          });
        }

        logger.info('Fetched all locations');
        res.status(200).json(locations);
      } catch (error) {
        logger.error(`Failed to fetch locations: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch locations' });
      }
    };
  }

  create() {
    return async (req: Request, res: Response) => {
      try {
        const location = await Location.create(req.body);
        res.status(201).json(location);
      } catch (error) {
        logger.error(`Failed to create location: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create location' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const location = await Location.findByIdAndUpdate(id, req.body, {
          new: true,
        });
        res.status(200).json(location);
      } catch (error) {
        logger.error(`Failed to update location: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to update location' });
      }
    };
  }

  delete() {
    return async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        await Location.findByIdAndUpdate(id, { deleted: true });
        res.status(200).json({ message: 'Location deleted successfully' });
      } catch (error) {
        logger.error(`Failed to delete location: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to delete location' });
      }
    };
  }

  get() {
    return async (req: Request, res: Response) => {
      try {
        const location = await Location.findById(req.params.id);
        res.status(200).json(location);
      } catch (error) {
        logger.error(`Failed to get location: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to get location' });
      }
    };
  }

  getLocationDetailsByGoogleMapsApi() {
    return async (req: Request, res: Response) => {
      try {
        const { latitude, longitude } = req.query;

        // // Validate required parameters
        if (!latitude || !longitude) {
          res.status(500).json({
            error: 'Failed to get location details from Google Maps API',
          });
        }

        // // Validate that coordinates are valid numbers
        const lat = parseFloat(String(latitude));
        const lng = parseFloat(String(longitude));

        const response = await axios.get(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${process.env.GOOGLE_MAPS_API_KEY}`,
        );

        logger.info(
          'Successfully fetched location details from Google Maps API',
        );

        // Extract country code from the response
        let countryCode = null;
        if (response.data.results && response.data.results.length > 0) {
          const addressComponents = response.data.results[0].address_components;
          const countryComponent = addressComponents.find(
            (component: { types: string[] }) =>
              component.types.includes('country'),
          );
          if (countryComponent) {
            countryCode = countryComponent.short_name;
          }
        }

        // Return the original response data along with the extracted country code
        res.status(200).json({
          ...response.data,
          countryPhoneCode:
            countryCodes.find((c) => c.iso === countryCode)?.value || null,
        });
      } catch (error) {
        logger.error(
          `Failed to get Google Maps API: ${(error as Error).message}`,
        );
        res.status(500).json({
          error: 'Failed to get location details from Google Maps API',
        });
      }
    };
  }
}
