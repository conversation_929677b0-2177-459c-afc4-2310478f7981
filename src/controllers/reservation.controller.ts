import { Request, Response } from 'express';
import logger from '../utils/logger';
import Reservation, {
  IReservation,
  ReservationStatusEnum,
} from '../models/reservation.model';
import RoomType, { IRoomType } from '../models/room-type.model';
import Package, { IPackage } from '../models/package.model';
import mongoose, { Types } from 'mongoose';
import DomainValue, { IDomainValue } from '../models/domain-value.model';
import { AuthRequest } from '../middlewares/auth.middleware';
import UserSchema from '../models/user.model';
import RateCardSchema, { IRateCard } from '../models/rate-card.model';
import { validateRefundInputs } from '../utils/cancellation';
import OTPSchema, { IOtp, OtpPurposeEnum } from '../models/otp-model';
import { generateRandomOTP } from '../utils/otpUtils';
import Jo<PERSON> from 'joi';
import { EmailService } from '../services/email.service';
import PropertyModel from '../models/property.model';
import { MASTER_COMPANY_ID } from '../constants';
import { checkRoomAvailability } from '../utils/availability';

export class ReservationController {
  private static Instance: ReservationController;
  private emailService: EmailService;

  public static getInstance(): ReservationController {
    if (!ReservationController.Instance) {
      ReservationController.Instance = new ReservationController();
    }
    return ReservationController.Instance;
  }

  private constructor() {
    this.emailService = new EmailService();
  }

  private getOtpValidationSchema = Joi.object({
    reservationCode: Joi.string().required(),
  });

  private verifyOtpValidationSchema = Joi.object({
    reservationCode: Joi.string().required(),
    otp: Joi.string().required(),
  });

  private updateValidationSchema = Joi.object({
    guestDetails: Joi.array()
      .items(
        Joi.object({
          firstName: Joi.string().optional(),
          lastName: Joi.string().optional(),
          gender: Joi.string().valid('male', 'female', 'other').optional(),
          email: Joi.string().email().optional(),
          phone: Joi.object({
            countryCode: Joi.string().required(),
            phoneNumber: Joi.string().required(),
          }).optional(),
        }).unknown(true),
      )
      .optional(),
  });

  getAvailable() {
    return async (req: Request, res: Response) => {
      try {
        const { propertyId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(propertyId)) {
          logger.warn(`Invalid propertyId format: ${propertyId}`);
          res.status(400).json({ error: 'Invalid property ID format' });
          return;
        }

        const {
          duration,
          noOfAdults,
          noOfChildren,
          startDateTime,
          endDateTime,
        } = req.query;

        const packageFilter: mongoose.FilterQuery<IPackage> = {
          propertyId: propertyId,
        };

        if (duration) {
          packageFilter.duration = { $gte: parseInt(duration as string, 10) };
        }

        if (noOfAdults) {
          packageFilter.noOfAdults = parseInt(noOfAdults as string, 10);
        }

        if (noOfChildren) {
          packageFilter.noOfChildren = parseInt(noOfChildren as string, 10);
        }
        if (startDateTime && endDateTime) {
          const start = new Date(startDateTime as string);
          const end = new Date(endDateTime as string);
          packageFilter.duration = {
            $gte: Math.ceil(
              (end.getTime() - start.getTime()) / (1000 * 60 * 60),
            ),
          };
        }

        const packages: (IPackage & {
          roomTypeId: IRoomType;
          taxes: IDomainValue[];
          amenities: IDomainValue[];
        })[] = await Package.find(
          packageFilter,
          {},
          { populate: ['roomTypeId', 'amenities', 'taxes'] },
        );

        const packageIds = packages.map((pkg) => pkg._id);
        const rateCards =
          startDateTime && endDateTime
            ? await RateCardSchema.find({
                packageId: { $in: packageIds },
                dateTime: {
                  $gte: new Date(startDateTime as string).toISOString(),
                  $lte: new Date(endDateTime as string).toISOString(),
                },
              }).lean()
            : [];

        const rateCardMap = rateCards.reduce(
          (acc, rateCard) => {
            const packageId = rateCard.packageId.toString();
            if (!acc[packageId] || rateCard.price > acc[packageId]) {
              acc[packageId] = rateCard.price;
            }
            return acc;
          },
          {} as Record<string, number>,
        );

        const roomTypePackages = packages.reduce(
          (acc, pkg) => {
            const roomTypeId = pkg.roomTypeId._id.toString();
            if (!acc[roomTypeId]) {
              acc[roomTypeId] = [];
            }
            const updatedPkg = {
              ...pkg.toObject(),
              rateCardPrice:
                rateCardMap[pkg._id.toString()] !== undefined
                  ? rateCardMap[pkg._id.toString()]
                  : null,
            };
            acc[roomTypeId].push(updatedPkg);
            return acc;
          },
          {} as Record<string, unknown[]>,
        );

        const roomTypes = await RoomType.find({
          propertyId,
          active: true,
        });

        let roomTypesWithPackages = roomTypes.map((roomType) => ({
          ...roomType.toObject(),
          packages: roomTypePackages[roomType._id.toString()] || [],
        }));

        if (startDateTime && endDateTime) {
          const availabilityResults = await checkRoomAvailability({
            propertyId,
            roomTypes,
            startDateTime: startDateTime as string,
            endDateTime: endDateTime as string,
          });

          // Update room types with availability
          roomTypesWithPackages = roomTypesWithPackages
            .map((roomType) => {
              const availabilityResult = availabilityResults.find(
                (result) => result.roomTypeId === roomType._id.toString(),
              );
              const availableRooms = availabilityResult?.availableRooms || 0;

              return {
                ...roomType,
                available: availableRooms,
                packages: roomType.packages.filter(() => availableRooms > 0),
              };
            })
            .filter((rt) => rt.available > 0);
        }

        logger.info('Fetched all availability with packages');
        res.status(200).json(roomTypesWithPackages);
      } catch (error) {
        logger.error(
          `Failed to fetch availability: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to fetch rooms' });
      }
    };
  }

  block() {
    return async (req: Request, res: Response) => {
      try {
        const { propertyId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(propertyId)) {
          logger.warn(`Invalid ID format: propertyId=${propertyId}`);
          res.status(400).json({ error: 'Invalid ID format' });
          return;
        }

        const packageIds = req.body.reservations.map(
          (reservation: Record<string, unknown>) => reservation.packageId,
        );
        if (
          !packageIds ||
          !Array.isArray(packageIds) ||
          packageIds.length === 0
        ) {
          logger.warn('No packageId provided in the request body');
          res.status(400).json({ error: 'No packageId provided' });
          return;
        }

        const packages = await Package.find({
          _id: { $in: packageIds },
        }).lean();
        const taxIds = packages.flatMap((pkg) => pkg.taxes);
        const taxes = await DomainValue.find({ _id: { $in: taxIds } }).lean();

        if (!packages || packages.length === 0) {
          logger.warn(`No packages found for packageId=${packageIds}`);
          res.status(404).json({ error: 'No packages found' });
          return;
        }

        const bookingDetails: IReservation = req.body;
        bookingDetails.propertyId = new mongoose.Types.ObjectId(propertyId);
        bookingDetails.status = ReservationStatusEnum.BLOCKED;
        bookingDetails.reservationCode = '0';
        bookingDetails.groupReservationId =
          new mongoose.Types.ObjectId().toString();

        // Check availability before blocking
        const roomTypeIds = bookingDetails.reservations.map(
          (resp) =>
            resp.roomTypeId ||
            packages.find(
              (pkg) => pkg._id.toString() === resp.packageId.toString(),
            )?.roomTypeId,
        );
        const roomTypes = await RoomType.find({ _id: { $in: roomTypeIds } });

        // Check availability for each reservation
        for (const reservation of bookingDetails.reservations) {
          const roomType = roomTypes.find(
            (rt) => rt._id.toString() === reservation.roomTypeId?.toString(),
          );
          if (!roomType) continue;

          const availabilityResults = await checkRoomAvailability({
            propertyId,
            roomTypes: [roomType],
            startDateTime: reservation.startDateTime.toISOString(),
            endDateTime: reservation.endDateTime.toISOString(),
          });

          const availableRooms = availabilityResults[0]?.availableRooms || 0;

          if (availableRooms <= 0) {
            logger.warn(
              `No rooms available for roomType ${roomType.name} during requested period`,
            );
            res.status(400).json({
              error: `No rooms available for ${roomType.name} during the requested period`,
            });
            return;
          }
        }

        const rateCardQueries = bookingDetails.reservations
          .map((reservation) => {
            const startDate = new Date(reservation.startDateTime);
            const endDate = new Date(reservation.endDateTime);
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
              logger.warn(
                `Invalid dates for reservation: packageId=${reservation.packageId}`,
              );
              return null;
            }
            return {
              packageId: new mongoose.Types.ObjectId(reservation.packageId),
              startDate: startDate.toISOString().split('T')[0],
              endDate: endDate.toISOString().split('T')[0],
            };
          })
          .filter((query) => query !== null);

        const rateCards: IRateCard[] =
          rateCardQueries.length > 0
            ? await RateCardSchema.find({
                $or: rateCardQueries.map((query) => ({
                  packageId: query.packageId,
                  date: { $gte: query.startDate, $lte: query.endDate },
                })),
              }).lean()
            : [];

        const rateCardMap = rateCards.reduce(
          (acc, rateCard) => {
            const packageId = rateCard.packageId.toString();
            if (!acc[packageId]) {
              acc[packageId] = rateCard.price;
            }
            return acc;
          },
          {} as Record<string, number>,
        );

        bookingDetails.reservations.forEach((reservation) => {
          const pkg = packages.find(
            (pkgData) =>
              pkgData._id.toString() === reservation.packageId.toString(),
          );
          const packageTax = taxes.filter((tax) =>
            pkg?.taxes.map((t) => t.toString()).includes(tax._id.toString()),
          );

          reservation.status = ReservationStatusEnum.BLOCKED;
          reservation.startDateTime = new Date(reservation.startDateTime);
          reservation.endDateTime = new Date(reservation.endDateTime);

          reservation.price =
            rateCardMap[reservation.packageId.toString()] !== undefined
              ? rateCardMap[reservation.packageId.toString()]
              : pkg?.price || 0;

          reservation.taxes = packageTax || [];
          reservation.tax = reservation.taxes.reduce((acc: number, tax) => {
            const taxValue = Number(tax.value) || 0;
            return acc + (reservation.price * taxValue) / 100;
          }, 0);
          reservation.totalAmount = reservation.price + reservation.tax;
          reservation.roomTypeId = new Types.ObjectId(pkg?.roomTypeId);
        });

        const newReservation = new Reservation({
          ...bookingDetails,
        });

        await newReservation.save();

        logger.info('Booking successful', newReservation);
        res.status(201).json({
          message: 'Booking successful',
          bookingDetails: newReservation,
        });
      } catch (error) {
        logger.error(`Failed to book reservation: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to book reservation' });
      }
    };
  }

  confirm() {
    return async (req: Request, res: Response) => {
      try {
        const { propertyId } = req.params;
        const updatedReservation = await Reservation.findOneAndUpdate(
          { _id: req.params.id, propertyId },
          { status: 'confirmed' },
          { new: true },
        );
        res.status(200).json(updatedReservation);
      } catch (error) {
        logger.error(
          `Failed to confirm reservation: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to confirm reservation' });
      }
    };
  }

  release() {
    return async (req: Request, res: Response) => {
      try {
        const { propertyId } = req.params;
        const updatedReservation = await Reservation.findOneAndUpdate(
          { _id: req.params.id, propertyId },
          { status: 'cancelled' },
          { new: true },
        );
        res.status(200).json(updatedReservation);
      } catch (error) {
        logger.error(
          `Failed to release reservation: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to release reservation' });
      }
    };
  }

  get() {
    return async (req: Request, res: Response) => {
      try {
        const reservation = await Reservation.findById(req.params.id).populate(
          'propertyId',
        );
        if (!reservation) {
          logger.warn(`Reservation with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Reservation not found' });
          return;
        }
        logger.info(`Fetched reservation: ${reservation.id}`);
        res.status(200).json(reservation);
      } catch (error) {
        logger.error(
          `Failed to fetch reservation: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to fetch reservation' });
      }
    };
  }

  getByPropertyId() {
    return async (req: Request, res: Response) => {
      try {
        const { propertyId } = req.params;
        let query = {};

        if (propertyId !== MASTER_COMPANY_ID) {
          query = { propertyId };
        }

        if (!mongoose.Types.ObjectId.isValid(propertyId)) {
          logger.warn(`Invalid propertyId format: ${propertyId}`);
          res.status(400).json({ error: 'Invalid property ID format' });
          return;
        }
        const reservations = await Reservation.find(query)
          .populate('propertyId')
          .populate([
            {
              path: 'reservations.roomTypeId',
            },
            {
              path: 'reservations.packageId',
            },
            {
              path: 'reservations.taxes',
            },
            {
              path: 'propertyId',
              populate: 'address.locationId',
            },
          ])
          .sort({ updatedAt: -1 });

        logger.info(`Fetched reservations for propertyId=${propertyId}`);
        res.status(200).json(reservations);
      } catch (error) {
        logger.error(
          `Failed to fetch reservations: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to fetch reservations' });
      }
    };
  }

  getByUser() {
    return async (req: AuthRequest, res: Response) => {
      try {
        const userId = req.user?.userId;

        if (!userId) {
          logger.warn(`User not found`);
          res.status(404).json({ error: 'User not found' });
          return;
        }

        if (!mongoose.Types.ObjectId.isValid(userId)) {
          logger.warn(`Invalid userId format: ${userId}`);
          res.status(400).json({ error: 'Invalid user ID format' });
          return;
        }

        const user = await UserSchema.findById(userId);

        if (!user) {
          res.status(404).json({ error: 'User not found' });
          return;
        }

        const reservations = await Reservation.find({
          'bookerDetails.email': user.email,
        })
          .populate([
            {
              path: 'reservations.roomTypeId',
            },
            {
              path: 'reservations.packageId',
            },
            {
              path: 'propertyId',
            },
            {
              path: 'reservations.taxes',
            },
          ])
          .sort({ createdAt: -1 });
        logger.info(`Fetched reservations for userId=${userId}`);
        res.status(200).json(reservations);
      } catch (error) {
        logger.error(
          `Failed to fetch reservations: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to fetch reservations' });
      }
    };
  }

  getRefundableAmount() {
    return async (req: Request, res: Response) => {
      try {
        const { reservationId } = req.params;
        const { cancellationTime } = req.query;

        const validationResult = await validateRefundInputs(
          reservationId,
          cancellationTime as string,
          res,
        );
        if (!validationResult) return;

        const { refundPolicy, refundableAmount } = validationResult;

        res.status(200).json({
          refundableAmount,
          refundablePercentage: refundPolicy.refund_percent || 0,
          description: refundPolicy.description,
        });
      } catch (error) {
        logger.error(
          `Failed to fetch refundable amount: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to fetch refundable amount' });
      }
    };
  }

  getOtp() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.getOtpValidationSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const { reservationCode } = req.body;

        const reservation = await Reservation.findOne({ reservationCode });

        if (!reservation) {
          logger.warn(`Reservation with code ${reservationCode} not found`);
          res.status(404).json({ error: 'Reservation not found' });
          return;
        }

        const userEmail = reservation.bookerDetails.email;

        const purpose = OtpPurposeEnum.CANCEL_BOOKING;

        const otp = generateRandomOTP(6);

        const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

        const existingOTP = await OTPSchema.findOne({
          userEmail,
          reservationCode,
          purpose,
        });
        if (existingOTP) {
          existingOTP.otp = otp;
          existingOTP.expiresAt = expiresAt;
          existingOTP.createdAt = new Date();
          await existingOTP.save();
        } else {
          const newOTP: IOtp = new OTPSchema({
            userEmail,
            reservationCode,
            purpose,
            otp,
            expiresAt,
          });
          await newOTP.save();
        }

        await this.emailService.sendOtpVerificationEmail(
          userEmail,
          reservation.bookerDetails.firstName,
          otp,
        );

        res.status(200).json({
          message: 'OTP generated successfully',
        });
      } catch (error) {
        logger.error('Error generating OTP:', error);
        res.status(500).json({
          error: 'Internal server error',
        });
      }
    };
  }

  verifyOtp() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.verifyOtpValidationSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const { reservationCode, otp } = req.body;

        const existingOTP = await OTPSchema.findOne({
          reservationCode,
          purpose: OtpPurposeEnum.CANCEL_BOOKING,
          otp,
          expiresAt: { $gt: new Date() },
        });

        if (!existingOTP) {
          logger.warn(
            `Invalid OTP for reservation with code ${reservationCode}`,
          );
          res.status(400).json({ error: 'Invalid OTP' });
          return;
        }

        const reservation = await Reservation.findOne({ reservationCode });

        if (!reservation) {
          logger.warn(`Reservation with code ${reservationCode} not found`);
          res.status(404).json({ error: 'Reservation not found' });
          return;
        }

        await existingOTP.deleteOne();

        res.status(200).json(reservation);
      } catch (error) {
        logger.error(`Failed to verify OTP: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to verify OTP' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.updateValidationSchema.validate(req.body, {
          abortEarly: false,
        });
        if (error) {
          const errorMessage = error.details
            .map((detail) => detail.message)
            .join(', ');
          res.status(400).json({ error: errorMessage });
          return;
        }

        const reservation = await Reservation.findById(req.params.id);
        if (!reservation) {
          res.status(404).json({ error: 'Reservation not found' });
          return;
        }

        if (
          reservation.reservations.some(
            (resp) => new Date(resp.startDateTime).getTime() < Date.now(),
          )
        ) {
          res.status(400).json({ error: 'Cannot update past reservation' });
          return;
        }

        const property = await PropertyModel.findById(reservation.propertyId);
        if (!property) {
          res.status(404).json({ error: 'Property not found' });
          return;
        }

        if (req.body.guestDetails && Array.isArray(req.body.guestDetails)) {
          await Reservation.updateOne(
            { _id: req.params.id },
            { $set: { 'reservations.0.guestDetails': req.body.guestDetails } },
            { runValidators: true },
          );
        }

        const updatedReservation = await Reservation.findById(
          req.params.id,
        ).populate([
          { path: 'reservations.roomTypeId' },
          { path: 'reservations.packageId' },
          { path: 'propertyId' },
          { path: 'reservations.taxes' },
        ]);
        if (!updatedReservation) {
          res.status(404).json({ error: 'Reservation not found after update' });
          return;
        }

        await this.emailService.sendUpdateReservationEmail(
          updatedReservation.bookerDetails.email,
          updatedReservation,
          property,
        );

        res.status(200).json(updatedReservation);
      } catch (error) {
        logger.error(
          `Failed to update reservation: ${(error as Error).message}`,
        );
        res.status(500).json({ error: 'Failed to update reservation' });
      }
    };
  }
}
