import { Request, Response } from 'express';
import logger from '../utils/logger';
import Company, { ICompany } from '../models/company.model';
import Joi from 'joi';

const companySchema = Joi.object({
  name: Joi.string().required().min(2).max(50),
  description: Joi.string(),
  website: Joi.string(),
  logoUrl: Joi.string(),
  email: Joi.string().email(),
  fax: Joi.string(),
  phone: Joi.object({
    countryCode: Joi.string().required(),
    phoneNumber: Joi.string().required(),
  }).required(),
  address: Joi.object({
    address1: Joi.string().required(),
    address2: Joi.string(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    country: Joi.string().required(),
    zipcode: Joi.string().required(),
    latitude: Joi.number(),
    longitude: Joi.number(),
    placeId: Joi.string().optional(),
  }).required(),
  poc: Joi.object({
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.object({
      countryCode: Joi.string().required(),
      phoneNumber: Joi.string().required(),
    }).required(),
  }).required(),
});

export class CompanyController {
  private static instance: CompanyController;

  public static getInstance(): CompanyController {
    if (!CompanyController.instance) {
      CompanyController.instance = new CompanyController();
    }
    return CompanyController.instance;
  }

  private constructor() {}

  getAll() {
    return async (_req: Request, res: Response) => {
      try {
        const companies: ICompany[] = await Company.find();
        logger.info('Fetched all companies');
        res.status(200).json(companies);
      } catch (error) {
        logger.error(`Failed to fetch companies: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch companies' });
      }
    };
  }

  get() {
    return async (req: Request, res: Response) => {
      try {
        const company = await Company.findById(req.params.id);
        if (!company) {
          logger.warn(`Company not found with id: ${req.params.id}`);
          return res.status(404).json({ error: 'Company not found' });
        }
        logger.info(`Fetched company with id: ${req.params.id}`);
        res.status(200).json(company);
      } catch (error) {
        logger.error(`Failed to fetch company: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch company' });
      }
    };
  }

  create() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = companySchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const company: ICompany = req.body;

        // Check if company exists before trying to save
        const existingCompany = await Company.findOne({ name: company.name });
        if (existingCompany) {
          logger.warn(`Company with name ${company.name} already exists`);
          res
            .status(400)
            .json({ error: 'Company with this name already exists' });
          return;
        }

        const newCompany = new Company(company);
        await newCompany.save();

        logger.info(`Company created: ${newCompany.name}`);
        res.status(201).json(newCompany);
      } catch (error) {
        // @ts-expect-error error is of unknown type
        if (error.code === 11000) {
          logger.warn(`Duplicate company name: ${req.body.name}`);
          res
            .status(400)
            .json({ error: 'Company with this name already exists' });
          return;
        }

        logger.error(`Failed to create company: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create company' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const company = await Company.findByIdAndUpdate(
          req.params.id,
          req.body,
          { new: true, runValidators: true },
        );
        if (!company) {
          logger.warn(`Company not found with id: ${req.params.id}`);
          return res.status(404).json({ error: 'Company not found' });
        }
        logger.info(`Updated company with id: ${req.params.id}`);
        res.status(200).json(company);
      } catch (error) {
        logger.error(`Failed to update company: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to update company' });
      }
    };
  }

  delete() {
    return async (req: Request, res: Response) => {
      try {
        const company = await Company.findByIdAndDelete(req.params.id);
        if (!company) {
          logger.warn(`Company not found with id: ${req.params.id}`);
          return res.status(404).json({ error: 'Company not found' });
        }
        logger.info(`Deleted company with id: ${req.params.id}`);
        res.status(204).send();
      } catch (error) {
        logger.error(`Failed to delete company: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to delete company' });
      }
    };
  }
}
