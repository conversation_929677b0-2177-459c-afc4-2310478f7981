import { Request, Response } from 'express';
import UserSchema from '../models/user.model';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import Joi from 'joi';
import logger from '../utils/logger';
import { EmailService } from '../services/email.service';
import Property from '../models/property.model';
import UserTokenDocument from '../models/user-token.model';
import { AuthRequest } from '../middlewares/auth.middleware';
import { admin } from '../config/firebase';
import { JWT_SECRET } from '../constants';

interface TokenValidationResponse {
  email?: string;
  firstName: string;
  lastName: string;
}

interface JwtPayloadWithUserId {
  userId: string;
}

export class AuthenticationController {
  private static instance: AuthenticationController;
  private emailService: EmailService;

  private constructor() {
    this.emailService = new EmailService();
  }
  public static getInstance(): AuthenticationController {
    if (!AuthenticationController.instance) {
      AuthenticationController.instance = new AuthenticationController();
    }
    return AuthenticationController.instance;
  }

  private readonly registerSchema = Joi.object({
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string()
      .min(8)
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,
      )
      .message(
        'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character',
      )
      .required(),
    phone: Joi.object({
      countryCode: Joi.string().required(),
      phoneNumber: Joi.string().required(),
    }).required(),
    type: Joi.string().optional().default('user').valid('user', 'merchant'),
  });

  private readonly loginSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required(),
  });

  private readonly forgotPasswordSchema = Joi.object({
    email: Joi.string().email().required(),
  });

  private readonly resetPasswordSchema = Joi.object({
    newPassword: Joi.string().min(6).required(),
  });

  private readonly changePasswordSchema = Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: Joi.string().min(8).required(),
  });

  private readonly searchIfEmailExistsSchema = Joi.object({
    email: Joi.string().email().required(),
  });

  private generateId(length: number): string {
    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    return Array.from(
      { length },
      () => chars[Math.floor(Math.random() * chars.length)],
    ).join('');
  }

  register() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.registerSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const { firstName, lastName, email, password, phone, type } = req.body;
        const userName = this.generateId(10);

        const existingUser = await UserSchema.findOne({ email });
        if (existingUser) {
          logger.warn(`User with email ${email} already exists`);
          res.status(400).json({ error: 'User already exists' });
          return;
        }

        const hashedPassword = await bcrypt.hash(password, 10);
        const newUser = new UserSchema({
          firstName,
          lastName,
          email,
          userName,
          password: hashedPassword,
          phone,
          role: type === 'merchant' ? ['merchant'] : ['user'],
        });

        await newUser.save();

        // Send confirmation email
        try {
          await this.emailService.sendConfirmationEmail(email, firstName);
        } catch (emailError) {
          logger.warn(
            `User registered but failed to send confirmation email: ${(emailError as Error).message}`,
          );
        }

        logger.info(`User registered successfully: ${email}`);
        res.status(201).json({
          message: 'User registered successfully',
        });
      } catch (error) {
        logger.error(`Registration failed: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to register user' });
      }
    };
  }

  login() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.loginSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const { email, password } = req.body;

        const user = await UserSchema.findOne({ email });

        if (!user) {
          logger.warn(`Login attempt with invalid email: ${email}`);
          res.status(400).json({ error: 'Invalid credentials' });
          return;
        }

        const properties = await Property.find({ 'poc.email': user.email });

        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
          logger.warn(`Login attempt with invalid password for user: ${email}`);
          res.status(400).json({ error: 'Invalid credentials' });
          return;
        }

        const token = jwt.sign({ userId: user._id }, JWT_SECRET, {
          expiresIn: '1h',
        });

        logger.info(`User logged in successfully: ${email}`);
        res.status(200).json({
          token,
          user: {
            id: user._id,
            username: user.userName,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            role: user.role,
            properties: properties || [],
          },
        });
      } catch (error) {
        logger.error(`Login failed: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to login' });
      }
    };
  }

  forgotPassword() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.forgotPasswordSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }
        const { email } = req.body;
        const user = await UserSchema.findOne({ email });
        if (!user) {
          throw new Error('User not found');
        }

        const token = jwt.sign({ userId: user._id }, JWT_SECRET, {
          expiresIn: '1h',
        });

        await UserTokenDocument.deleteMany({ userId: user._id });

        await UserTokenDocument.create({
          userId: user._id,
          token: token,
        });

        await this.emailService.sendForgotPasswordEmail(
          user.email,
          user.firstName,
          token,
        );

        res.status(200).json({
          message: 'Email sent for reset password',
        });
      } catch (error) {
        logger.error(`Forgot password failed: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to initiate password reset' });
      }
    };
  }

  resetPassword() {
    return async (req: Request, res: Response) => {
      try {
        const token = req.header('Authorization')?.split(' ')[1];

        if (!token) {
          res.status(401).json({ message: 'No token, authorization denied' });
          return;
        }

        const tokenData = jwt.verify(token, JWT_SECRET) as JwtPayloadWithUserId;

        const userId = tokenData.userId;

        const { error } = this.resetPasswordSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }
        const { newPassword } = req.body;

        const resetDoc = await UserTokenDocument.findOne({ userId });

        if (!resetDoc) {
          throw new Error('Invalid or expired token');
        }

        if (!tokenData) {
          throw new Error('Invalid token');
        }

        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await UserSchema.findByIdAndUpdate(userId, {
          password: hashedPassword,
        });
        await UserTokenDocument.deleteMany({ userId });

        res.status(200).json({ message: 'Password reset successfully' });
      } catch (error) {
        logger.error(`Reset password failed: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to reset password' });
      }
    };
  }

  changePassword() {
    return async (req: AuthRequest, res: Response) => {
      try {
        const { error } = this.changePasswordSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }
        if (!req.user?.userId) {
          res.status(401).json({ message: 'Unauthorized' });
          return;
        }
        const { currentPassword, newPassword } = req.body;

        const user = await UserSchema.findById(req.user.userId);
        if (!user || !(await bcrypt.compare(currentPassword, user.password))) {
          throw new Error('Invalid credentials');
        }

        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await UserSchema.findByIdAndUpdate(req.user.userId, {
          password: hashedPassword,
        });

        res.status(200).json({ message: 'Password changed successfully' });
      } catch (error) {
        logger.error(`Change password failed: ${(error as Error).message}`);
        res.status(500).json({ error: (error as Error).message });
      }
    };
  }

  checkUserExists() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.searchIfEmailExistsSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }
        const { email } = req.body;
        const user = await UserSchema.findOne({ email });
        if (!user) {
          res.status(200).json({ data: null });
          return;
        }
        res.status(400).json({ message: 'User Already Exists' });
      } catch (e) {
        logger.error('Failed to search user: ', e);
        res.status(500).json({ error: 'Failed to search user' });
      }
    };
  }

  loginWithFirebase() {
    return async (req: Request, res: Response) => {
      try {
        const { firebaseToken } = req.body;
        const { phone: phoneNumber, countryCode } = req.body.phone || {};

        if (!firebaseToken) {
          res.status(400).json({ error: 'Firebase token is required' });
          return;
        }

        const verifiedUser = await this.verifyFirebaseToken(firebaseToken);

        const isEmail = !!verifiedUser.email;
        let existingUser = await UserSchema.findOne(
          !isEmail
            ? {
                phone: {
                  phoneNumber: phoneNumber,
                  countryCode: countryCode,
                },
              }
            : {
                email: verifiedUser.email,
              },
        );

        const hashedPassword = await bcrypt.hash(
          this.generateRandomPassword(),
          10,
        );
        if (!existingUser) {
          const newUser = {
            userName: this.generateId(10),
            firstName: verifiedUser.firstName,
            lastName: verifiedUser.lastName,
            email: verifiedUser.email,
            password: hashedPassword,
            phone: {
              phoneNumber: phoneNumber,
              countryCode: countryCode,
            },
            role: ['user'],
          };
          existingUser = await UserSchema.create(newUser);
        }

        const properties = existingUser.email
          ? await Property.find({ 'poc.email': existingUser.email })
          : [];

        const token = jwt.sign({ userId: existingUser._id }, JWT_SECRET, {
          expiresIn: '1h',
        });

        res.status(200).json({
          token,
          user: {
            id: existingUser._id,
            username: existingUser.userName,
            email: existingUser.email,
            firstName: existingUser.firstName,
            lastName: existingUser.lastName,
            phone: existingUser.phone,
            role: existingUser.role,
            properties: properties || [],
          },
        });
      } catch (error) {
        logger.error('Error login with Firebase:', error);
        res.status(500).json({
          error: 'Internal server error',
        });
      }
    };
  }

  async verifyFirebaseToken(idToken: string): Promise<TokenValidationResponse> {
    try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);

      const [firstName, ...lastName] = (
        decodedToken.name || 'Hi Traveller'
      ).split(' ');
      return {
        email: decodedToken.email,
        firstName,
        lastName: lastName.join(' '),
      };
    } catch (error) {
      logger.error(`Failed to verify Firebase token: ${error}`);

      throw new Error('Invalid Firebase token');
    }
  }

  private generateRandomPassword(): string {
    return Math.random().toString(36).slice(-10);
  }
}
