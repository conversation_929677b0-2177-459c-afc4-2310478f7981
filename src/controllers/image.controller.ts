import { Request, Response } from 'express';
import logger from '../utils/logger';
import { getImageUrl, uploadImage } from '../services/s3.service';

export class ImageController {
  private static Instance: ImageController;

  public static getInstance(): ImageController {
    if (!ImageController.Instance)
      ImageController.Instance = new ImageController();
    return ImageController.Instance;
  }

  private constructor() {}

  upload() {
    return async (req: Request, res: Response) => {
      try {
        if (!req.file) {
          logger.warn(`No file uploaded.`);
          res.status(400).json({ error: 'No file uploaded.' });
          return;
        }
        const key = await uploadImage(req.file);
        logger.info('Uploaded image successfully');
        res.status(200).json({
          message: 'Image uploaded successfully',
          key,
        });
      } catch (error) {
        logger.error(`Failed to upload image: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to upload image' });
      }
    };
  }

  preview() {
    return async (req: Request, res: Response) => {
      try {
        const url = await getImageUrl(req.params.key);
        res.json({ url });
      } catch (error) {
        logger.error(`Failed to get image URL: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to get image URL' });
      }
    };
  }
}
