import { Request, Response } from 'express';
import logger from '../utils/logger';
import <PERSON><PERSON> from 'joi';
import Room, { IRoom } from '../models/room.model';

export class RoomController {
  private static Instance: RoomController;

  public static getInstance(): RoomController {
    if (!RoomController.Instance) {
      RoomController.Instance = new RoomController();
    }
    return RoomController.Instance;
  }

  private constructor() {}

  private readonly roomSchema = Joi.object({
    code: Joi.string().required(),
    name: Joi.string().required(),
    description: Joi.string().optional(),
  });

  getAll() {
    return async (_req: Request, res: Response) => {
      try {
        const rooms: IRoom[] = await Room.find();
        logger.info('Fetched all rooms');
        res.status(200).json(rooms);
      } catch (error) {
        logger.error(`Failed to fetch rooms: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch rooms' });
      }
    };
  }

  get() {
    return async (req: Request, res: Response) => {
      try {
        const room = await Room.findById(req.params.id);
        if (!room) {
          logger.warn(`Room with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Room not found' });
          return;
        }
        logger.info(`Fetched room: ${room.name}`);
        res.status(200).json(room);
      } catch (error) {
        logger.error(`Failed to fetch room: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to fetch room' });
      }
    };
  }

  create() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.roomSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const room: IRoom = req.body;
        const existingRoom = await Room.findOne({ name: room.name });
        if (existingRoom) {
          logger.warn(`Room with name ${room.name} already exists`);
          res.status(400).json({ error: 'Room already exists' });
          return;
        }

        const newRoom = new Room({
          code: room.code,
          name: room.name,
          description: room.description,
        });

        await newRoom.save();
        logger.info(`Room created: ${newRoom.name}`);
        res.status(201).json(newRoom);
      } catch (error) {
        logger.error(`Failed to create room: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to create room' });
      }
    };
  }

  update() {
    return async (req: Request, res: Response) => {
      try {
        const { error } = this.roomSchema.validate(req.body);
        if (error) {
          res.status(400).json({ error: error.details[0].message });
          return;
        }

        const room = await Room.findById(req.params.id);
        if (!room) {
          logger.warn(`Room with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Room not found' });
          return;
        }

        const existingRoom = await Room.findOne({
          name: req.body.name,
          _id: { $ne: req.params.id },
        });

        if (existingRoom) {
          logger.warn(`Room with name ${req.body.name} already exists`);
          res.status(400).json({ error: 'Room name already exists' });
          return;
        }

        const updatedRoom = await Room.findByIdAndUpdate(
          req.params.id,
          req.body,
          { new: true },
        );

        logger.info(`Room updated: ${updatedRoom?.name}`);
        res.status(200).json(updatedRoom);
      } catch (error) {
        logger.error(`Failed to update room: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to update room' });
      }
    };
  }

  delete() {
    return async (req: Request, res: Response) => {
      try {
        const room = await Room.findByIdAndDelete(req.params.id);

        if (!room) {
          logger.warn(`Room with id ${req.params.id} not found`);
          res.status(404).json({ error: 'Room not found' });
          return;
        }

        logger.info(`Room deleted: ${room.name}`);
        res.status(204).send();
      } catch (error) {
        logger.error(`Failed to delete room: ${(error as Error).message}`);
        res.status(500).json({ error: 'Failed to delete room' });
      }
    };
  }
}
