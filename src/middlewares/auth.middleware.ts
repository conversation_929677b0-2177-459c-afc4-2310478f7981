import { NextFunction, Request, Response } from 'express';
import jwt, { JwtPayload } from 'jsonwebtoken';
import logger from '../utils/logger';
import { JWT_SECRET } from '../constants';

export interface AuthRequest extends Request {
  user?: {
    userId: string;
  };
}

export interface UserJwtPayload extends JwtPayload {
  userId: string;
}

export const authMiddleware = (
  req: AuthRequest,
  res: Response,
  next: NextFunction,
): void => {
  const token = req.header('Authorization')?.split(' ')[1];

  if (!token) {
    res.status(401).json({ message: 'No token, authorization denied' });
    return;
  }

  if (!JWT_SECRET) {
    logger.error('JWT_SECRET is not defined');
    res.status(500).json({ message: 'Server configuration error' });
    return;
  }

  try {
    const decoded = verifyToken(token);
    if (!decoded) {
      res.status(401).json({ message: 'Invalid token' });
      return;
    }
    req.user = { userId: decoded.userId };
    next();
  } catch (error) {
    logger.error(`Failed to verify token: ${(error as Error).message}`);
    res.status(401).json({ message: 'Invalid token' });
  }
};

const verifyToken = (token: string): UserJwtPayload | null => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload;
    if ('userId' in decoded) {
      return decoded as UserJwtPayload;
    }
    return null;
  } catch (_error) {
    return null;
  }
};
