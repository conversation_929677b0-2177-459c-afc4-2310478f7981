import { Router } from 'express';
import { PropertyController } from '../controllers/property.controller';

const router: Router = Router();

const propertyController = PropertyController.getInstance();

router
  .route('/')
  .get(propertyController.getAll())
  .post(propertyController.create());

router
  .route('/:id')
  .get(propertyController.get())
  .put(propertyController.update())
  .delete(propertyController.delete());

router.route('/:id/customFields').patch(propertyController.patchCustomFields());

router.route('/:id/status').patch(propertyController.patchStatus());

export default router;
