import { Router } from 'express';
import { LocationController } from '../controllers/location.controller';

const router: Router = Router();

const locationController = LocationController.getInstance();

router
  .route('/')
  .get(locationController.getAll())
  .post(locationController.create());

router
  .route('/:id')
  .get(locationController.get())
  .put(locationController.update())
  .delete(locationController.delete());

router
  .route('/google/maps')
  .get(locationController.getLocationDetailsByGoogleMapsApi());

export default router;
