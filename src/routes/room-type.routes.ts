import { Router } from 'express';
import { RoomTypeController } from '../controllers/room-type.controller';

const router: Router = Router({ mergeParams: true });
const roomTypeController = RoomTypeController.getInstance();
router
  .route('/')
  .get(roomTypeController.getAll())
  .post(roomTypeController.create());

router
  .route('/:id')
  .get(roomTypeController.get())
  .put(roomTypeController.update())
  .delete(roomTypeController.delete())
  .patch(roomTypeController.updateStatus());

export default router;
