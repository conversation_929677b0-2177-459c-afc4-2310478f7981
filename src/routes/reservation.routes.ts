import { Router } from 'express';
import { ReservationController } from '../controllers/reservation.controller';

const router: Router = Router({ mergeParams: true });
const reservationController = ReservationController.getInstance();
router.get('/:propertyId/available', reservationController.getAvailable());
router.post('/:propertyId/block', reservationController.block());
router.patch('/:propertyId/:id/confirm', reservationController.confirm());
router.patch('/:propertyId/:id/release', reservationController.release());
router.get('/:id', reservationController.get());
router.get('/property/:propertyId', reservationController.getByPropertyId());
router.get(
  '/:reservationId/refundable-amount',
  reservationController.getRefundableAmount(),
);
router.post('/cancel/get-otp', reservationController.getOtp());
router.post('/cancel/verify-otp', reservationController.verifyOtp());
router.patch('/:id', reservationController.update());

export default router;
