import { Router } from 'express';
import { DomainValueController } from '../controllers/domain-value.controller';

const router: Router = Router();

const domainValueController = DomainValueController.getInstance();

router
  .route('/')
  .get(domainValueController.getAll())
  .post(domainValueController.create());

router
  .route('/:id')
  .get(domainValueController.getById())
  .put(domainValueController.update())
  .delete(domainValueController.delete());

router.route('/level/:level').get(domainValueController.getByLevel());
router
  .route('/categoryId/:categoryId')
  .get(domainValueController.getByCategoryId());
router
  .route('/categoryId/:categoryId/propertyId/:propertyId')
  .get(domainValueController.getByCategoryIdAndPropertyId());

export default router;
