import { Router } from 'express';
import { PolicyController } from '../controllers/policy.controller';

const router: Router = Router();

const policyController = PolicyController.getInstance();

// Policy CRUD routes (with auth middleware)
router
  .route('/')
  .get(policyController.getAll())
  .post(policyController.create());

router
  .route('/:id')
  .get(policyController.getById())
  .put(policyController.update())
  .delete(policyController.delete());

export default router;
