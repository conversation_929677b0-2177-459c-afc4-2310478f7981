import { Router } from 'express';
import applicationRoutes from './application.routes';
import authenticationRoutes from './authentication.routes';
import companyRoutes from './company.routes';
import imageRoutes from './image.routes';
import propertyRoutes from './property.routes';
import reservationRoutes from './reservation.routes';
import roomTypeRoutes from './room-type.routes';
import packageRoutes from './package.routes';
import userRoutes from './user.routes';
import domainValueRoutes from './domain-value.routes';
import policyRoutes from './policy.routes';

//Dummy
import locationRoutes from './location.routes';
import flightRoutes from './flight.routes';
import paymentRoutes from './payment.routes';
import rateCardRoutes from './rate-card.routes';
import roomTypeAvailabilityRoutes from './room-availability.routes';
import financeRoutes from './finance.route';

const router: Router = Router();
router.use('/v1/applications', applicationRoutes);
router.use('/v1/images', imageRoutes);
router.use('/v1/companies', companyRoutes);
router.use('/v1/properties', propertyRoutes);
router.use('/v1/properties/:propertyId/room-types', roomTypeRoutes);
router.use('/v1/properties/:propertyId/packages', packageRoutes);
router.use('/v1/properties/:propertyId/rate-cards', rateCardRoutes);
router.use(
  '/v1/properties/:propertyId/room-type-availability',
  roomTypeAvailabilityRoutes,
);
router.use('/v1/reservations', reservationRoutes);
router.use('/v1/payments', paymentRoutes);

router.use('/v1/users', userRoutes);
router.use('/v1/domain-values', domainValueRoutes);
router.use('/v1/authentication', authenticationRoutes);
router.use('/v1/policy', policyRoutes);

//
router.use('/v1/locations', locationRoutes);
router.use('/v1/flights', flightRoutes);
router.use('/v1/finance', financeRoutes);

export default router;
