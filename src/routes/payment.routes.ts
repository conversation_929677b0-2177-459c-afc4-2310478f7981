import { Router } from 'express';
import { PaymentController } from '../controllers/payment.controller';

const paymentController = PaymentController.getInstance();

const router: Router = Router();
router.route('/').post(paymentController.create());

router
  .route('/:id')
  .get(paymentController.get())
  .put(paymentController.update())
  .patch(paymentController.updateStatus())
  .delete(paymentController.delete());

router.route('/:paymentProvider/checkout').post(paymentController.checkout());

router.post(
  '/:reservationId/:paymentProvider/refund',
  paymentController.refund(),
);

export default router;
