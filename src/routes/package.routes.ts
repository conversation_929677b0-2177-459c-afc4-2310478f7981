import { Router } from 'express';
import { PackageController } from '../controllers/package.controller';

const router: Router = Router({ mergeParams: true });

const packageController = PackageController.getInstance();

router
  .route('/')
  .get(packageController.getAll())
  .post(packageController.create());

router
  .route('/:id')
  .get(packageController.get())
  .put(packageController.update())
  .delete(packageController.delete());

export default router;
