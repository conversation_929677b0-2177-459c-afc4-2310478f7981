import { Router } from 'express';
import { AuthenticationController } from '../controllers/authentication.controller';
import { authMiddleware } from '../middlewares/auth.middleware';

const router: Router = Router();

const authenticationController = AuthenticationController.getInstance();

router.post('/register', authenticationController.register());
router.post('/login', authenticationController.login());
router.post('/forgot-password', authenticationController.forgotPassword());
router.post('/reset-password', authenticationController.resetPassword());
router.post(
  '/change-password',
  authMiddleware,
  authenticationController.changePassword(),
);
router.post('/check-user', authenticationController.checkUserExists());
router.post('/firebase', authenticationController.loginWithFirebase());

export default router;
