import { Router } from 'express';
import { RoomController } from '../controllers/room.controller';

const router: Router = Router();
const roomController = RoomController.getInstance();
router.route('/').get(roomController.getAll()).post(roomController.create());

router
  .route('/:id')
  .get(roomController.get())
  .put(roomController.update())
  .delete(roomController.delete());

export default router;
