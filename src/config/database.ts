import mongoose from 'mongoose';
import logger from '../utils/logger';
import { MONGO_URI } from '../constants';

const connectDB = async (): Promise<void> => {
  try {
    const mongoURI = MONGO_URI;
    await mongoose.connect(mongoURI, {});
    logger.info('MongoDB connected successfully');
  } catch (error) {
    logger.error(`MongoDB connection error: ${(error as Error).message}`);
    process.exit(1);
  }
};

export default connectDB;
