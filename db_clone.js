const mongoose = require('mongoose');

async function migrateDatabase() {
  const sourceUri =
    'mongodb+srv://OpusSupervisor:<EMAIL>?retryWrites=true&w=majority&ssl=true';
  const targetUri =
    'mongodb+srv://admin:<EMAIL>?retryWrites=true&w=majority&ssl=true';
  const sourceDbName = 'Stay-Transit-Dev';
  const targetDbName = 'Stay-Transit-QA';

  let sourceConn, targetConn;

  try {
    // Connect to both databases
    sourceConn = await mongoose.createConnection(sourceUri).asPromise();
    targetConn = await mongoose.createConnection(targetUri).asPromise();

    console.log('Connected to both databases.');

    // Use the source database
    const sourceDb = sourceConn.useDb(sourceDbName);
    const targetDb = targetConn.useDb(targetDbName);

    // Get all collections from the source database
    const collections = await sourceDb.db.listCollections().toArray();

    for (const collectionInfo of collections) {
      const collectionName = collectionInfo.name;
      console.log(`Migrating collection: ${collectionName}`);

      // Define a temporary schema for the collection (since Mongoose requires schemas)
      const tempSchema = new mongoose.Schema({}, { strict: false });
      const SourceModel = sourceDb.model(
        collectionName,
        tempSchema,
        collectionName,
      );
      const TargetModel = targetDb.model(
        collectionName,
        tempSchema,
        collectionName,
      );

      // Fetch all documents from the source collection
      const documents = await SourceModel.find({}).lean().exec();

      // Insert documents into the target collection in batches
      if (documents.length > 0) {
        const batchSize = 1000; // Adjust batch size based on your needs
        for (let i = 0; i < documents.length; i += batchSize) {
          const batch = documents.slice(i, i + batchSize);
          await TargetModel.insertMany(batch, { ordered: false });
          console.log(
            `Migrated ${batch.length} documents to ${collectionName}`,
          );
        }
      } else {
        console.log(`No documents found in ${collectionName}`);
      }

      // Copy indexes (optional)
      const indexes = await SourceModel.collection.indexes();
      for (const index of indexes) {
        if (index.name !== '_id_') {
          // Skip default _id index
          await TargetModel.collection.createIndex(index.key, {
            unique: index.unique || false,
            sparse: index.sparse || false,
            background: true,
            name: index.name,
          });
          console.log(`Created index ${index.name} on ${collectionName}`);
        }
      }

      // Clean up the temporary model to avoid Mongoose model conflicts
      delete sourceDb.models[collectionName];
      delete targetDb.models[collectionName];
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Close connections
    if (sourceConn) await sourceConn.close();
    if (targetConn) await targetConn.close();
    console.log('Connections closed.');
  }
}

// Run the migration
migrateDatabase().catch((err) => console.error('Migration error:', err));
